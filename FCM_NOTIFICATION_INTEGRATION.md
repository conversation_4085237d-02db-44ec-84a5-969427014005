# FCM Notification Integration for Flutter Mobile App

## Overview

The cross-platform calling system has been updated to send FCM notifications directly to Flutter mobile devices using the FCM v1 API. The notification payload structure matches exactly what the Flutter app expects for seamless integration.

## Implementation Details

### FCM v1 API Integration

**API Endpoint:**
```
https://fcm.googleapis.com/v1/projects/cmt-meet-fd921/messages:send
```

**Authorization:**
- Uses Bearer token with JWT token from `calls/send-call` response
- Content-Type: `application/json`

### Flutter App Expected Payload Structure

The FCM message includes these specific data fields that the Flutter app expects:

```javascript
{
  "message": {
    "token": "target_device_fcm_token",
    "data": {
      "callerAgoraId": "caller_agora_id",        // Caller's Agora ID
      "callerName": "Caller Display Name",        // Caller's display name
      "callerJob": "Software Engineer",           // Caller's job title
      "callerCompany": "Company Name",            // Caller's company
      "fcmToken": "caller_fcm_token",            // Caller's FCM token
      "channelName": "agora_channel_name",        // Agora channel name
      "token": "rtc_token_for_call",             // RTC token for the call
      "callType": "voice|video",                  // "voice" or "video"
      "callUUID": "unique_call_identifier",       // Unique call identifier
      "jwtToken": "jwt_authentication_token",     // JWT authentication token
      "status": "CALLING",                        // Call status
      "type": "CALL_RESPONSE",                    // Message type
      "click_action": "FLUTTER_NOTIFICATION_CLICK" // Click action handler
    },
    "notification": {
      "title": "Incoming Voice/Video Call",
      "body": "Caller Name is calling you"
    },
    "android": {
      "priority": "high",
      "notification": {
        "channel_id": "call_channel",
        "priority": "high",
        "sound": "default"
      }
    },
    "apns": {
      "headers": {
        "apns-priority": "10"
      },
      "payload": {
        "aps": {
          "alert": {
            "title": "Incoming Voice/Video Call",
            "body": "Caller Name is calling you"
          },
          "sound": "default",
          "badge": 1,
          "content-available": 1,
          "category": "CALL_CATEGORY"
        }
      }
    }
  }
}
```

## Updated Service Methods

### 1. sendMobileCallNotification()

**Enhanced Method Signature:**
```javascript
async sendMobileCallNotification(fcmToken, callData, jwtToken, callerInfo = {})
```

**Parameters:**
- `fcmToken`: Target device FCM token
- `callData`: Call information including callId, channelName, isVideoCall, etc.
- `jwtToken`: JWT token for FCM API authorization
- `callerInfo`: Additional caller information (agoraId, jobTitle, company, etc.)

**Features:**
- ✅ Direct FCM v1 API integration
- ✅ Flutter app compatible payload structure
- ✅ Platform-specific settings (Android/iOS)
- ✅ Fallback to backend method if direct API fails
- ✅ Comprehensive error handling and logging

### 2. sendCallCancellationNotification()

**Method for Call Cancellation:**
```javascript
async sendCallCancellationNotification(fcmToken, callId, jwtToken)
```

**Payload:**
```javascript
{
  "data": {
    "callUUID": "call_id",
    "status": "CANCELLED",
    "type": "CALL_RESPONSE",
    "click_action": "FLUTTER_NOTIFICATION_CLICK"
  }
}
```

### 3. sendCallRejectionNotification()

**Method for Call Rejection:**
```javascript
async sendCallRejectionNotification(fcmToken, callId, jwtToken)
```

**Payload:**
```javascript
{
  "data": {
    "callUUID": "call_id",
    "status": "REJECTED",
    "type": "CALL_RESPONSE",
    "click_action": "FLUTTER_NOTIFICATION_CLICK"
  }
}
```

## Integration with Existing Hooks

### useCall Hook Updates

**Enhanced Call Initiation:**
```javascript
callDetails = await crossPlatformCallService.initiateCall({
  callerId: user?.agoraid,
  calleeId: recipientUserId || recipientId,
  eventCode: eventCode,
  channelName: channelName,
  isVideoCall: false,
  callerName: user?.fullname || "Unknown",
  callerJobTitle: user?.jobtitle || "",      // New field
  callerCompany: user?.company || ""         // New field
});
```

### useVideoCall Hook Updates

**Enhanced Video Call Initiation:**
```javascript
callDetails = await crossPlatformCallService.initiateCall({
  callerId: user?.agoraid,
  calleeId: recipientId,
  eventCode: eventCode,
  channelName: channelName,
  isVideoCall: true,
  callerName: user?.fullname || "Unknown",
  callerJobTitle: user?.jobtitle || "",      // New field
  callerCompany: user?.company || ""         // New field
});
```

## Call Flow Integration

### 1. Call Initiation Flow

```mermaid
sequenceDiagram
    participant Web as Web App
    participant Backend as Backend API
    participant FCM as FCM Service
    participant Mobile as Flutter App

    Web->>Backend: POST /calls/send-call
    Backend->>Web: {callerFCMToken, calleeFCMToken, rtcToken, jwtToken}
    Web->>FCM: POST /v1/projects/cmt-meet-fd921/messages:send
    FCM->>Mobile: Push Notification
    Mobile->>Mobile: Show Incoming Call UI
```

### 2. Call State Notifications

**Call Cancellation:**
```javascript
// When caller cancels call
await crossPlatformCallService.cancelCall(callId);
// Automatically sends CANCELLED notification to mobile
```

**Call Rejection:**
```javascript
// When callee rejects call
await crossPlatformCallService.handleCallRejected(callId, rejecterId);
// Automatically sends REJECTED notification to caller
```

## Error Handling and Fallbacks

### Primary Method: Direct FCM v1 API
- Uses fetch API to send notifications directly
- Requires JWT token for authorization
- Handles HTTP response codes and errors

### Fallback Method: Backend API
- Falls back to `/calls/send-fcm-notification` endpoint
- Used when direct FCM API fails
- Maintains backward compatibility

### Error Scenarios Handled:
1. **Invalid JWT Token**: Falls back to backend method
2. **Network Errors**: Falls back to backend method
3. **FCM API Rate Limits**: Logged and handled gracefully
4. **Invalid FCM Tokens**: Logged for debugging

## Testing and Validation

### Test Component: FCMNotificationTest.js

**Features:**
- ✅ FCM payload structure validation
- ✅ Service method availability testing
- ✅ FCM API endpoint accessibility testing
- ✅ Call initiation simulation
- ✅ Comprehensive logging and debugging

**Usage:**
```javascript
import FCMNotificationTest from './components/FCMNotificationTest';

// Add to your app for testing
<FCMNotificationTest />
```

### Manual Testing Checklist

1. **Web-to-Mobile Voice Call**
   - [ ] FCM notification received on mobile
   - [ ] Notification contains all required data fields
   - [ ] Mobile app shows incoming call UI
   - [ ] Call can be accepted/rejected

2. **Web-to-Mobile Video Call**
   - [ ] FCM notification received on mobile
   - [ ] callType field shows "video"
   - [ ] Mobile app shows video call UI
   - [ ] Video call functionality works

3. **Call State Synchronization**
   - [ ] Call cancellation notification received
   - [ ] Call rejection notification sent to caller
   - [ ] Status fields correctly set (CALLING, CANCELLED, REJECTED)

## Flutter App Requirements

### FCM Message Handling

The Flutter app should handle these message types:

```dart
FirebaseMessaging.onMessage.listen((RemoteMessage message) {
  final data = message.data;
  
  switch (data['type']) {
    case 'CALL_RESPONSE':
      switch (data['status']) {
        case 'CALLING':
          showIncomingCallScreen(
            callerAgoraId: data['callerAgoraId'],
            callerName: data['callerName'],
            callerJob: data['callerJob'],
            callerCompany: data['callerCompany'],
            channelName: data['channelName'],
            rtcToken: data['token'],
            callType: data['callType'],
            callUUID: data['callUUID'],
          );
          break;
        case 'CANCELLED':
          dismissIncomingCall(data['callUUID']);
          break;
        case 'REJECTED':
          handleCallRejected(data['callUUID']);
          break;
      }
      break;
  }
});
```

### Required Data Fields Usage

- **callerAgoraId**: Use for Agora RTC connection
- **callerName**: Display in call UI
- **callerJob**: Display in caller info
- **callerCompany**: Display in caller info
- **channelName**: Use for Agora channel connection
- **token**: Use as RTC token for Agora
- **callType**: Determine voice/video call UI
- **callUUID**: Use for call tracking and responses
- **jwtToken**: Use for API authentication

## Security Considerations

### JWT Token Usage
- JWT tokens are used for FCM API authorization
- Tokens should have appropriate expiration times
- Tokens are obtained from secure backend endpoint

### FCM Token Security
- FCM tokens are device-specific and automatically managed
- Tokens are transmitted over HTTPS only
- Invalid tokens are handled gracefully

### Data Privacy
- Only necessary call information is included in notifications
- No sensitive user data is transmitted via FCM
- All communication uses secure channels

## Monitoring and Debugging

### Logging Features
- Comprehensive console logging for all FCM operations
- Error tracking for failed notifications
- Success confirmation for delivered notifications
- Payload structure validation logging

### Debug Information
- FCM token truncation in logs for security
- API response status codes and messages
- Fallback method usage tracking
- Call state transition logging

## Performance Considerations

### Optimization Features
- Direct FCM API calls reduce backend load
- Efficient payload structure minimizes data transfer
- Fallback mechanisms ensure reliability
- Error handling prevents blocking operations

### Scalability
- Direct FCM integration scales with Google's infrastructure
- Backend fallback provides additional reliability
- Efficient error handling prevents cascading failures
- Comprehensive logging aids in monitoring and optimization

The updated FCM notification system provides robust, scalable, and Flutter-compatible push notifications for cross-platform calling functionality.
