import React, { useState } from 'react';
import { crossPlatformCallService } from '../services/CrossPlatformCallService';

/**
 * Test component for FCM notification functionality
 * This component helps test the FCM v1 API integration with Flutter app payload structure
 */
const FCMNotificationTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testCallId, setTestCallId] = useState(null);

  const addTestResult = (test, result, details = '') => {
    setTestResults(prev => [...prev, {
      test,
      result,
      details,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testFCMPayloadStructure = () => {
    try {
      // Test the FCM payload structure matches Flutter app expectations
      const mockCallData = {
        callId: 'test_call_123',
        callerId: 'caller_agora_id',
        callerName: 'Test Caller',
        channelName: 'test_channel_456',
        isVideoCall: false,
        rtcToken: 'mock_rtc_token'
      };

      const mockCallerInfo = {
        agoraId: 'caller_agora_id',
        callerFCMToken: 'caller_fcm_token',
        jobTitle: 'Software Engineer',
        company: 'Test Company'
      };

      const mockJwtToken = 'mock_jwt_token';

      // Simulate the FCM payload structure
      const expectedPayload = {
        message: {
          token: 'target_fcm_token',
          data: {
            callerAgoraId: mockCallerInfo.agoraId,
            callerName: mockCallData.callerName,
            callerJob: mockCallerInfo.jobTitle,
            callerCompany: mockCallerInfo.company,
            fcmToken: mockCallerInfo.callerFCMToken,
            channelName: mockCallData.channelName,
            token: mockCallData.rtcToken,
            callType: mockCallData.isVideoCall ? 'video' : 'voice',
            callUUID: mockCallData.callId,
            jwtToken: mockJwtToken,
            status: 'CALLING',
            type: 'CALL_RESPONSE',
            click_action: 'FLUTTER_NOTIFICATION_CLICK'
          },
          notification: {
            title: `Incoming ${mockCallData.isVideoCall ? 'Video' : 'Voice'} Call`,
            body: `${mockCallData.callerName} is calling you`
          },
          android: {
            priority: 'high',
            notification: {
              channel_id: 'call_channel',
              priority: 'high',
              sound: 'default'
            }
          },
          apns: {
            headers: {
              'apns-priority': '10'
            },
            payload: {
              aps: {
                alert: {
                  title: `Incoming ${mockCallData.isVideoCall ? 'Video' : 'Voice'} Call`,
                  body: `${mockCallData.callerName} is calling you`
                },
                sound: 'default',
                badge: 1,
                'content-available': 1,
                category: 'CALL_CATEGORY'
              }
            }
          }
        }
      };

      addTestResult(
        'FCM Payload Structure',
        'PASS',
        `Payload structure matches Flutter app expectations. Contains all required fields: ${Object.keys(expectedPayload.message.data).join(', ')}`
      );

      console.log('Expected FCM Payload:', expectedPayload);

    } catch (error) {
      addTestResult('FCM Payload Structure', 'ERROR', error.message);
    }
  };

  const testCrossPlatformCallService = async () => {
    try {
      addTestResult('Cross-Platform Service Test', 'RUNNING', 'Testing service availability...');

      // Test service methods exist
      const serviceExists = typeof crossPlatformCallService.initiateCall === 'function';
      const fcmMethodExists = typeof crossPlatformCallService.sendMobileCallNotification === 'function';
      const cancelMethodExists = typeof crossPlatformCallService.sendCallCancellationNotification === 'function';

      if (serviceExists && fcmMethodExists && cancelMethodExists) {
        addTestResult(
          'Cross-Platform Service Test',
          'PASS',
          'All required service methods are available'
        );
      } else {
        addTestResult(
          'Cross-Platform Service Test',
          'FAIL',
          `Missing methods: ${!serviceExists ? 'initiateCall ' : ''}${!fcmMethodExists ? 'sendMobileCallNotification ' : ''}${!cancelMethodExists ? 'sendCallCancellationNotification' : ''}`
        );
      }

    } catch (error) {
      addTestResult('Cross-Platform Service Test', 'ERROR', error.message);
    }
  };

  const testFCMApiEndpoint = async () => {
    try {
      addTestResult('FCM API Endpoint Test', 'RUNNING', 'Testing FCM API endpoint accessibility...');

      const fcmApiUrl = 'https://fcm.googleapis.com/v1/projects/cmt-meet-fd921/messages:send';
      
      // Test if the endpoint is reachable (will fail due to auth, but should not be network error)
      const response = await fetch(fcmApiUrl, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer invalid_token_for_test',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ message: { token: 'test' } })
      });

      // We expect a 401 or 403 (auth error), not a network error
      if (response.status === 401 || response.status === 403) {
        addTestResult(
          'FCM API Endpoint Test',
          'PASS',
          `FCM API endpoint is reachable. Status: ${response.status} (expected auth error)`
        );
      } else {
        addTestResult(
          'FCM API Endpoint Test',
          'WARN',
          `Unexpected response status: ${response.status}`
        );
      }

    } catch (error) {
      if (error.name === 'TypeError' && error.message.includes('fetch')) {
        addTestResult('FCM API Endpoint Test', 'FAIL', 'Network error - FCM API not reachable');
      } else {
        addTestResult('FCM API Endpoint Test', 'ERROR', error.message);
      }
    }
  };

  const simulateCallInitiation = async () => {
    try {
      addTestResult('Call Initiation Simulation', 'RUNNING', 'Simulating call initiation...');

      const mockCallParams = {
        callerId: 'test_caller_agora_id',
        calleeId: 'test_callee_user_id',
        eventCode: 'TEST_EVENT_2024',
        channelName: 'test_channel_' + Date.now(),
        isVideoCall: false,
        callerName: 'Test Caller',
        callerJobTitle: 'Software Engineer',
        callerCompany: 'Test Company'
      };

      // This will likely fail due to backend not being available, but we can test the structure
      try {
        const result = await crossPlatformCallService.initiateCall(mockCallParams);
        setTestCallId(result.callId);
        addTestResult(
          'Call Initiation Simulation',
          'PASS',
          `Call initiated successfully. Call ID: ${result.callId}`
        );
      } catch (apiError) {
        // Expected to fail due to backend, but we can check if the error is from API call
        if (apiError.message.includes('Network Error') || apiError.message.includes('404') || apiError.message.includes('500')) {
          addTestResult(
            'Call Initiation Simulation',
            'WARN',
            'Backend API not available (expected in test environment). Service structure is correct.'
          );
        } else {
          addTestResult('Call Initiation Simulation', 'ERROR', apiError.message);
        }
      }

    } catch (error) {
      addTestResult('Call Initiation Simulation', 'ERROR', error.message);
    }
  };

  const runAllTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    testFCMPayloadStructure();
    await testCrossPlatformCallService();
    await testFCMApiEndpoint();
    await simulateCallInitiation();

    setIsRunningTests(false);
  };

  const clearResults = () => {
    setTestResults([]);
    setTestCallId(null);
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px', borderRadius: '8px' }}>
      <h3>FCM Notification Test Console</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <h4>FCM Configuration</h4>
        <p><strong>Project ID:</strong> cmt-meet-fd921</p>
        <p><strong>FCM API URL:</strong> https://fcm.googleapis.com/v1/projects/cmt-meet-fd921/messages:send</p>
        <p><strong>Expected Payload Fields:</strong> callerAgoraId, callerName, callerJob, callerCompany, fcmToken, channelName, token, callType, callUUID, jwtToken, status, type, click_action</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h4>Test Actions</h4>
        <button 
          onClick={runAllTests} 
          disabled={isRunningTests}
          style={{ margin: '5px', padding: '8px 16px', backgroundColor: '#007bff', color: 'white', border: 'none' }}
        >
          {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
        </button>
        
        <button 
          onClick={testFCMPayloadStructure}
          style={{ margin: '5px', padding: '8px 16px' }}
        >
          Test FCM Payload Structure
        </button>
        
        <button 
          onClick={testCrossPlatformCallService}
          style={{ margin: '5px', padding: '8px 16px' }}
        >
          Test Service Methods
        </button>
        
        <button 
          onClick={clearResults}
          style={{ margin: '5px', padding: '8px 16px' }}
        >
          Clear Results
        </button>
      </div>

      <div>
        <h4>Test Results</h4>
        <div style={{ maxHeight: '400px', overflowY: 'auto', border: '1px solid #ddd', padding: '10px' }}>
          {testResults.length === 0 ? (
            <p>No test results yet. Run some tests to see results here.</p>
          ) : (
            testResults.map((result, index) => (
              <div 
                key={index} 
                style={{ 
                  margin: '5px 0', 
                  padding: '8px', 
                  backgroundColor: result.result === 'PASS' ? '#d4edda' : 
                                   result.result === 'FAIL' ? '#f8d7da' : 
                                   result.result === 'ERROR' ? '#f5c6cb' : 
                                   result.result === 'WARN' ? '#fff3cd' : '#e2e3e5',
                  border: '1px solid #ccc',
                  borderRadius: '4px'
                }}
              >
                <strong>[{result.timestamp}] {result.test}:</strong> 
                <span style={{ 
                  color: result.result === 'PASS' ? '#155724' : 
                         result.result === 'FAIL' ? '#721c24' : 
                         result.result === 'ERROR' ? '#721c24' : 
                         result.result === 'WARN' ? '#856404' : '#6c757d',
                  fontWeight: 'bold',
                  marginLeft: '10px'
                }}>
                  {result.result}
                </span>
                {result.details && <div style={{ marginTop: '5px', fontSize: '0.9em' }}>{result.details}</div>}
              </div>
            ))
          )}
        </div>
      </div>

      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f8f9fa', border: '1px solid #dee2e6' }}>
        <h4>Flutter App Integration Notes</h4>
        <ul>
          <li><strong>FCM Data Fields:</strong> The Flutter app expects specific data fields in the FCM payload</li>
          <li><strong>Call Types:</strong> "voice" or "video" in the callType field</li>
          <li><strong>Status Values:</strong> "CALLING", "CANCELLED", "REJECTED" for different call states</li>
          <li><strong>Click Action:</strong> "FLUTTER_NOTIFICATION_CLICK" to handle notification taps</li>
          <li><strong>Platform Settings:</strong> High priority for Android, apns-priority 10 for iOS</li>
          <li><strong>Authorization:</strong> Uses JWT token from calls/send-call response as Bearer token</li>
        </ul>
      </div>
    </div>
  );
};

export default FCMNotificationTest;
