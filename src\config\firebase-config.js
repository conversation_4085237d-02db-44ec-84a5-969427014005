/**
 * Firebase Configuration
 * 
 * IMPORTANT: Replace these placeholder values with your actual Firebase project credentials
 * 
 * To get these values:
 * 1. Go to Firebase Console (https://console.firebase.google.com/)
 * 2. Select your project or create a new one
 * 3. Go to Project Settings > General > Your apps
 * 4. Add a web app if you haven't already
 * 5. Copy the configuration values
 * 
 * For FCM (Firebase Cloud Messaging):
 * 1. Go to Project Settings > Cloud Messaging
 * 2. Generate a new key pair for Web Push certificates
 * 3. Copy the VAPID key
 */

export const firebaseConfig = {
  // Replace with your Firebase project configuration
  apiKey: "AIzaSyAIkEyJ_S8vToYMO2b4UBFTTdb6fOpSSdQ",
  authDomain: "cmt-meet-fd921.firebaseapp.com",
  projectId: "cmt-meet-fd921",
  storageBucket: "cmt-meet-fd921.firebasestorage.app",
  messagingSenderId: "953784194836",
  appId: "1:953784194836:web:9fbc8a15ba903d49c51401"
};

export const vapidKey = "BNRhAjNoDm5MUeAAsx5c56YqNVmmGypzxm4sbXG17NpKDOvAfui5glK5zh9DA3i4rX7UxdLr23SmoLss5w-1P1Y";

// Validate configuration
export const validateFirebaseConfig = () => {
  const requiredFields = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
  const missingFields = requiredFields.filter(field => 
    !firebaseConfig[field] || firebaseConfig[field].includes('your-') || firebaseConfig[field].includes('-here')
  );

  if (missingFields.length > 0) {
    console.warn('Firebase configuration incomplete. Missing or placeholder values for:', missingFields);
    console.warn('Please update src/config/firebase-config.js with your actual Firebase project credentials');
    return false;
  }

  if (!vapidKey || vapidKey.includes('your-') || vapidKey.includes('-here')) {
    console.warn('VAPID key not configured. FCM push notifications will not work.');
    console.warn('Please update the VAPID key in src/config/firebase-config.js');
    return false;
  }

  return true;
};

// Environment-specific configurations
export const isDevelopment = process.env.NODE_ENV === 'development';
export const isProduction = process.env.NODE_ENV === 'production';

// FCM configuration
export const fcmConfig = {
  vapidKey: vapidKey,
  // Service worker path (relative to public folder)
  serviceWorkerPath: '/firebase-messaging-sw.js'
};

// Default notification settings
export const defaultNotificationSettings = {
  icon: '/logo192.png',
  badge: '/logo192.png',
  requireInteraction: true,
  tag: 'call-notification',
  actions: [
    {
      action: 'answer',
      title: 'Answer',
      icon: '/logo192.png'
    },
    {
      action: 'decline', 
      title: 'Decline',
      icon: '/logo192.png'
    }
  ]
};
