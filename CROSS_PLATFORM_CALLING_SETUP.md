# Cross-Platform Calling Setup Guide

This guide explains how to set up and configure the cross-platform calling functionality that enables web-to-web, web-to-mobile, and mobile-to-web calls using Agora.io and Firebase Cloud Messaging (FCM).

## Overview

The cross-platform calling system consists of:
- **Web Application**: React.js with Agora Web SDK
- **Mobile Applications**: Flutter with Agora Flutter SDK and FCM
- **Backend Integration**: calls/send-call endpoint and FCM notification service
- **Firebase Cloud Messaging**: Push notifications for mobile devices

## Prerequisites

1. **Firebase Project**: You need a Firebase project with FCM enabled
2. **Agora Project**: Existing Agora project with RTC and RTM enabled
3. **Backend API**: The calls/send-call endpoint must be implemented
4. **Mobile Apps**: Flutter mobile apps with Agora and FCM integration

## Setup Instructions

### 1. Firebase Configuration

#### Step 1: Create/Configure Firebase Project
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Create a new project or select existing project
3. Enable Cloud Messaging in the project

#### Step 2: Get Firebase Configuration
1. Go to Project Settings > General > Your apps
2. Add a web app if you haven't already
3. Copy the Firebase configuration object
4. Go to Project Settings > Cloud Messaging
5. Generate Web Push certificates and copy the VAPID key

#### Step 3: Update Configuration Files
Update `src/config/firebase-config.js` with your Firebase credentials:

```javascript
export const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-actual-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-actual-sender-id",
  appId: "your-actual-app-id"
};

export const vapidKey = "your-actual-vapid-key";
```

Also update `public/firebase-messaging-sw.js` with the same configuration.

### 2. Backend API Integration

#### Required Endpoint: `/calls/send-call`
Your backend must implement this endpoint with the following specification:

**Request:**
```json
{
  "callerId": "user_id_from_users_table",
  "calleeId": "user_id_from_users_table", 
  "eventCode": "event_code_from_event_data",
  "channelName": "agora_channel_name",
  "isVideoCall": true/false,
  "callerName": "caller_display_name",
  "timestamp": 1234567890
}
```

**Response:**
```json
{
  "success": true,
  "callId": "unique_call_identifier",
  "callerFCMToken": "caller_fcm_token",
  "calleeFCMToken": "callee_fcm_token",
  "rtcToken": "agora_rtc_token_for_channel",
  "jwtToken": "jwt_authentication_token"
}
```

#### Required Endpoint: `/calls/send-fcm-notification`
For sending FCM notifications to mobile devices:

**Request:**
```json
{
  "targetToken": "fcm_token_of_target_device",
  "callData": {
    "callId": "call_identifier",
    "callerId": "caller_user_id",
    "callerName": "caller_name",
    "channelName": "agora_channel",
    "isVideoCall": true/false,
    "timestamp": 1234567890
  },
  "notificationType": "call_request|call_cancelled"
}
```

### 3. Environment Variables (Optional)

Create a `.env` file in your project root for environment-specific configuration:

```env
REACT_APP_FIREBASE_API_KEY=your-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
REACT_APP_FIREBASE_APP_ID=your-app-id
REACT_APP_FIREBASE_VAPID_KEY=your-vapid-key
```

### 4. Integration with Existing Code

The cross-platform calling functionality is integrated into your existing call hooks:

#### Voice Calls
- `useCall` hook now uses `CrossPlatformCallService`
- Automatically sends FCM notifications to mobile devices
- Maintains backward compatibility with web-to-web calls

#### Video Calls  
- `useVideoCall` hook now uses `CrossPlatformCallService`
- Supports cross-platform video calling
- Maintains existing UI and functionality

### 5. Mobile App Requirements

Your Flutter mobile apps must:

1. **Handle FCM Notifications**: Listen for call_request notifications
2. **Agora Integration**: Use Agora Flutter SDK for RTC calls
3. **Call UI**: Display incoming call interface when notification received
4. **Response Handling**: Send call acceptance/rejection back to web

#### Sample FCM Notification Handler (Flutter):
```dart
FirebaseMessaging.onMessage.listen((RemoteMessage message) {
  if (message.data['type'] == 'call_request') {
    // Show incoming call UI
    showIncomingCallScreen(
      callId: message.data['callId'],
      callerId: message.data['callerId'],
      callerName: message.data['callerName'],
      channelName: message.data['channelName'],
      isVideoCall: message.data['isVideoCall'] == 'true',
    );
  }
});
```

## Usage

### Starting a Cross-Platform Call

The existing call functions now automatically support cross-platform calling:

```javascript
// Voice call - works for both web and mobile recipients
const { startCall } = useCall(userId, rtmToken, rtcToken, user);
await startCall(recipientId, recipientName);

// Video call - works for both web and mobile recipients  
const { startVideoCall } = useVideoCall(userId, rtmToken, rtcToken, user);
await startVideoCall(recipientId, recipientName);
```

### Handling Incoming Calls

Use the new cross-platform call hook:

```javascript
import { useCrossPlatformCall } from '../hooks/useCrossPlatformCall';

const {
  incomingCalls,
  acceptCall,
  rejectCall,
  hasIncomingCalls
} = useCrossPlatformCall();

// Handle incoming calls
if (hasIncomingCalls()) {
  incomingCalls.forEach(call => {
    // Show incoming call UI
    // User can accept or reject
  });
}
```

## Testing

### Testing Checklist

1. **Web-to-Web Calls**: ✅ Should work as before
2. **Web-to-Mobile Calls**: Test FCM notification delivery
3. **Mobile-to-Web Calls**: Test web app receiving calls
4. **Call State Sync**: Test acceptance/rejection across platforms
5. **Error Handling**: Test network failures and token issues

### Testing Tools

1. **Firebase Console**: Monitor FCM message delivery
2. **Browser DevTools**: Check console logs and network requests
3. **Mobile Device**: Test actual push notifications
4. **Multiple Browsers**: Test web-to-web functionality

## Troubleshooting

### Common Issues

1. **FCM Notifications Not Received**
   - Check Firebase configuration
   - Verify VAPID key is correct
   - Ensure service worker is registered
   - Check browser notification permissions

2. **Backend API Errors**
   - Verify calls/send-call endpoint is implemented
   - Check request/response format matches specification
   - Ensure proper authentication

3. **Token Issues**
   - Verify Agora RTC tokens are valid
   - Check token expiration times
   - Ensure proper channel name format

4. **Cross-Platform Sync Issues**
   - Check mobile app FCM integration
   - Verify call state management
   - Test network connectivity

### Debug Logs

Enable debug logging by setting:
```javascript
console.log('Cross-platform call debug mode enabled');
```

## Security Considerations

1. **FCM Tokens**: Store securely, refresh regularly
2. **RTC Tokens**: Use channel-specific tokens when possible
3. **User IDs**: Use proper user ID validation
4. **API Endpoints**: Implement proper authentication and rate limiting

## Performance Optimization

1. **Token Caching**: Cache RTC tokens appropriately
2. **FCM Batching**: Batch FCM notifications when possible
3. **Error Retry**: Implement exponential backoff for failed requests
4. **Memory Management**: Clean up call states after completion

## Support

For issues and questions:
1. Check browser console for error messages
2. Verify Firebase and Agora configurations
3. Test with simplified scenarios first
4. Check network connectivity and firewall settings
