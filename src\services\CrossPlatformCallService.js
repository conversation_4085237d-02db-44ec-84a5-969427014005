import { api } from './AuthService';

/**
 * Cross-Platform Call Service for managing calls between web and mobile platforms
 * This service integrates with the backend calls/send-call endpoint and handles FCM notifications
 */
export class CrossPlatformCallService {
  constructor() {
    this.pendingCalls = new Map(); // Track pending calls
  }

  /**
   * Initiate a cross-platform call using the calls/send-call endpoint
   * @param {Object} callParams - Call parameters
   * @param {string} callParams.callerId - Caller's user ID (users.id)
   * @param {string} callParams.calleeId - Callee's user ID (users.id)
   * @param {string} callParams.eventCode - Event code from eventData
   * @param {string} callParams.channelName - Agora channel name
   * @param {boolean} callParams.isVideoCall - Whether this is a video call
   * @param {string} callParams.callerName - Caller's display name
   * @param {string} callParams.callerJobTitle - Caller's job title (optional)
   * @param {string} callParams.callerCompany - Caller's company (optional)
   * @returns {Promise<Object>} Call details including tokens and FCM information
   */
  async initiateCall(callParams) {
    try {
      const {
        callerId,
        calleeId,
        eventCode,
        channelName,
        isVideoCall,
        callerName
      } = callParams;

      console.log('Initiating cross-platform call with params:', callParams);

      // Call the backend calls/send-call endpoint
      const response = await api.post('/calls/send-call', {
        callerId: callerId, // Use users.id instead of agoraId
        calleeId: calleeId, // Use users.id instead of agoraId
        eventCode: eventCode,
        channelName: channelName,
        isVideoCall: isVideoCall,
        callerName: callerName,
        timestamp: Date.now()
      });

      if (!response.data) {
        throw new Error('Invalid response from calls/send-call endpoint');
      }

      const callDetails = response.data;
      console.log('Call details received:', callDetails);

      // Expected response structure:
      // {
      //   callerFCMToken: "caller's FCM token",
      //   calleeFCMToken: "callee's FCM token", 
      //   rtcToken: "RTC token for the channel",
      //   jwtToken: "JWT token for authentication",
      //   callId: "unique call identifier",
      //   success: true
      // }

      // Store call details for tracking
      const callId = callDetails.callId || `call_${Date.now()}`;
      this.pendingCalls.set(callId, {
        ...callDetails,
        ...callParams,
        callId,
        startTime: Date.now(),
        status: 'initiated'
      });

      // If callee has FCM token, send push notification for mobile users
      if (callDetails.calleeFCMToken) {
        console.log('Sending FCM notification to mobile device');

        // Prepare caller information for FCM payload
        const callerInfo = {
          agoraId: callerId, // This should be the caller's Agora ID
          callerFCMToken: callDetails.callerFCMToken,
          jobTitle: callParams.callerJobTitle || '',
          company: callParams.callerCompany || ''
        };

        await this.sendMobileCallNotification(
          callDetails.calleeFCMToken,
          {
            callId,
            callerId,
            callerName,
            channelName,
            isVideoCall,
            timestamp: Date.now(),
            rtcToken: callDetails.rtcToken
          },
          callDetails.jwtToken,
          callerInfo
        );
      }

      return {
        success: true,
        callId,
        rtcToken: callDetails.rtcToken,
        jwtToken: callDetails.jwtToken,
        callerFCMToken: callDetails.callerFCMToken,
        calleeFCMToken: callDetails.calleeFCMToken,
        channelName,
        isVideoCall
      };

    } catch (error) {
      console.error('Error initiating cross-platform call:', error);
      throw new Error(`Failed to initiate call: ${error.message}`);
    }
  }

  /**
   * Send FCM notification to mobile device using FCM v1 API
   * @param {string} fcmToken - Target device FCM token
   * @param {Object} callData - Call information
   * @param {string} jwtToken - JWT token for FCM API authorization
   * @param {Object} callerInfo - Additional caller information
   * @returns {Promise<boolean>} Success status
   */
  async sendMobileCallNotification(fcmToken, callData, jwtToken, callerInfo = {}) {
    try {
      console.log('Sending FCM notification to mobile device:', {
        fcmToken: fcmToken.substring(0, 20) + '...',
        callId: callData.callId,
        callType: callData.isVideoCall ? 'video' : 'audio'
      });

      // FCM v1 API endpoint
      const fcmApiUrl = 'https://fcm.googleapis.com/v1/projects/cmt-meet-fd921/messages:send';

      // Prepare FCM payload structure expected by Flutter app
      const fcmPayload = {
        message: {
          token: fcmToken,
          data: {
            // Required fields for Flutter app
            callerAgoraId: callerInfo.agoraId || callData.callerId,
            callerName: callData.callerName || 'Unknown Caller',
            callerJob: callerInfo.jobTitle || '',
            callerCompany: callerInfo.company || '',
            fcmToken: callerInfo.callerFCMToken || '',
            channelName: callData.channelName,
            token: callData.rtcToken || '',
            callType: callData.isVideoCall ? 'video' : 'audio',
            callUUID: callData.callId,
            jwtToken: jwtToken,
            status: 'CALLING',
            type: 'CALL_RESPONSE',
            click_action: 'FLUTTER_NOTIFICATION_CLICK'
          },
          android: {
            priority: 'high'
          },
          apns: {
            headers: {
              'apns-priority': '10'
            },
            payload: {
              aps: {
                sound: 'default',
                badge: 1,
                'content-available': 1
              },
              voip: "1"
            }
          }
        }
      };

      console.log('FCM payload prepared:', {
        ...fcmPayload,
        message: {
          ...fcmPayload.message,
          token: fcmToken.substring(0, 20) + '...'
        }
      });

      // Send FCM notification using fetch API
      const response = await fetch(fcmApiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(fcmPayload)
      });

      const responseData = await response.json();

      if (response.ok) {
        console.log('FCM notification sent successfully:', responseData);
        return true;
      } else {
        console.error('FCM API error:', response.status, responseData);
      }

    } catch (error) {
      console.error('Error sending FCM notification:', error);
    }
  }

  /**
   * Handle call acceptance from mobile device
   * @param {string} callId - Call identifier
   * @param {string} accepterId - ID of user accepting the call
   */
  async handleCallAccepted(callId, accepterId) {
    const callDetails = this.pendingCalls.get(callId);
    if (callDetails) {
      callDetails.status = 'accepted';
      callDetails.acceptedAt = Date.now();
      callDetails.accepterId = accepterId;
      
      console.log('Call accepted:', callDetails);
      
      // Notify other components about call acceptance
      this.notifyCallAccepted(callDetails);
    }
  }

  /**
   * Handle call rejection from mobile device
   * @param {string} callId - Call identifier
   * @param {string} rejecterId - ID of user rejecting the call
   */
  async handleCallRejected(callId, rejecterId) {
    const callDetails = this.pendingCalls.get(callId);
    if (callDetails) {
      callDetails.status = 'rejected';
      callDetails.rejectedAt = Date.now();
      callDetails.rejecterId = rejecterId;

      console.log('Call rejected:', callDetails);

      // Send rejection notification to caller if needed
      if (callDetails.callerFCMToken && callDetails.jwtToken) {
        await this.sendCallRejectionNotification(
          callDetails.callerFCMToken,
          callId,
          callDetails.jwtToken
        );
      }

      // Clean up pending call
      this.pendingCalls.delete(callId);

      // Notify other components about call rejection
      this.notifyCallRejected(callDetails);
    }
  }

  /**
   * Handle call cancellation
   * @param {string} callId - Call identifier
   */
  async cancelCall(callId) {
    const callDetails = this.pendingCalls.get(callId);
    if (callDetails) {
      callDetails.status = 'cancelled';
      callDetails.cancelledAt = Date.now();
      
      console.log('Call cancelled:', callDetails);
      
      // Send cancellation notification to mobile device if needed
      if (callDetails.calleeFCMToken && callDetails.jwtToken) {
        await this.sendCallCancellationNotification(
          callDetails.calleeFCMToken,
          callId,
          callDetails.jwtToken
        );
      }
      
      // Clean up pending call
      this.pendingCalls.delete(callId);
      
      // Notify other components about call cancellation
      this.notifyCallCancelled(callDetails);
    }
  }

  /**
   * Send call cancellation notification using FCM v1 API
   * @param {string} fcmToken - Target device FCM token
   * @param {string} callId - Call identifier
   * @param {string} jwtToken - JWT token for authorization
   */
  async sendCallCancellationNotification(fcmToken, callId, jwtToken) {
    try {
      console.log('Sending call cancellation notification:', callId);

      const fcmApiUrl = 'https://fcm.googleapis.com/v1/projects/cmt-meet-fd921/messages:send';

      const fcmPayload = {
        message: {
          token: fcmToken,
          data: {
            callUUID: callId,
            status: 'CANCELLED',
            type: 'CALL_RESPONSE',
            click_action: 'FLUTTER_NOTIFICATION_CLICK'
          },
          notification: {
            title: 'Call Cancelled',
            body: 'The incoming call was cancelled'
          },
          android: {
            priority: 'high'
          },
          apns: {
            headers: {
              'apns-priority': '10'
            },
            payload: {
              aps: {
                alert: {
                  title: 'Call Cancelled',
                  body: 'The incoming call was cancelled'
                },
                sound: 'default',
                'content-available': 1
              }
            }
          }
        }
      };

      const response = await fetch(fcmApiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(fcmPayload)
      });

      if (response.ok) {
        console.log('Call cancellation notification sent successfully');
      } else {
        console.error('Failed to send call cancellation notification:', response.status);
      }

    } catch (error) {
      console.error('Error sending call cancellation notification:', error);
    }
  }

  /**
   * Send call rejection notification using FCM v1 API
   * @param {string} fcmToken - Target device FCM token
   * @param {string} callId - Call identifier
   * @param {string} jwtToken - JWT token for authorization
   */
  async sendCallRejectionNotification(fcmToken, callId, jwtToken) {
    try {
      console.log('Sending call rejection notification:', callId);

      const fcmApiUrl = 'https://fcm.googleapis.com/v1/projects/cmt-meet-fd921/messages:send';

      const fcmPayload = {
        message: {
          token: fcmToken,
          data: {
            callUUID: callId,
            status: 'REJECTED',
            type: 'CALL_RESPONSE',
            click_action: 'FLUTTER_NOTIFICATION_CLICK'
          },
          android: {
            priority: 'high'
          },
          apns: {
            headers: {
              'apns-priority': '10'
            },
            payload: {
              aps: {
                'content-available': 1
              }
            }
          }
        }
      };

      const response = await fetch(fcmApiUrl, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(fcmPayload)
      });

      if (response.ok) {
        console.log('Call rejection notification sent successfully');
      } else {
        console.error('Failed to send call rejection notification:', response.status);
      }

    } catch (error) {
      console.error('Error sending call rejection notification:', error);
    }
  }

  /**
   * Get call details by ID
   * @param {string} callId - Call identifier
   * @returns {Object|null} Call details or null if not found
   */
  getCallDetails(callId) {
    return this.pendingCalls.get(callId) || null;
  }

  /**
   * Get all pending calls
   * @returns {Array} Array of pending call details
   */
  getPendingCalls() {
    return Array.from(this.pendingCalls.values());
  }

  /**
   * Clear all pending calls
   */
  clearPendingCalls() {
    this.pendingCalls.clear();
  }

  // Event notification methods (to be implemented based on your event system)
  notifyCallAccepted(callDetails) {
    // Implement based on your event system
    console.log('Call accepted notification:', callDetails);
  }

  notifyCallRejected(callDetails) {
    // Implement based on your event system
    console.log('Call rejected notification:', callDetails);
  }

  notifyCallCancelled(callDetails) {
    // Implement based on your event system
    console.log('Call cancelled notification:', callDetails);
  }
}

// Export singleton instance
export const crossPlatformCallService = new CrossPlatformCallService();
