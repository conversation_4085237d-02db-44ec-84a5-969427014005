"use client";

import { useState, useEffect, useRef, useCallback } from "react";
import { VideoCallService } from "../services/VideoCallService";
import { CallService } from "../services/CallService";
import { checkTokensBeforeCall } from "../services/AuthService";
import { generateChannelName, validateChannelName } from "../utils/agoraConfig";
import { crossPlatformCallService } from "../services/CrossPlatformCallService";

// Define call status constants for better code readability
const CALL_STATUS = {
  IDLE: "idle",
  INCOMING: "incoming",
  RINGING: "ringing",
  CONNECTING: "connecting",
  CONNECTED: "connected",
  DISCONNECTED: "disconnected",
  ENDED: "ended",
  REJECTED: "rejected",
};

/**
 * Custom hook for managing video call functionality
 */
const useVideoCall = (userId, rtmToken, rtcToken, user) => {
  // State variables
  const [callStatus, setCallStatus] = useState(CALL_STATUS.IDLE);
  const [error, setError] = useState(null);
  const [activeCall, setActiveCall] = useState(null);
  const [incomingCall, setIncomingCall] = useState(null);
  const [localVideoTrack, setLocalVideoTrack] = useState(null);
  const [localAudioTrack, setLocalAudioTrack] = useState(null);
  const [remoteUsers, setRemoteUsers] = useState([]);
  const [isVideoEnabled, setIsVideoEnabled] = useState(true);
  const [isAudioEnabled, setIsAudioEnabled] = useState(true);
  const [callDuration, setCallDuration] = useState(0);
  const [callHistory, setCallHistory] = useState([]);

  // References
  const videoCallServiceRef = useRef(null);
  const callServiceRef = useRef(null);
  const callTimerRef = useRef(null);
  const currentCallRef = useRef(null);
  const remoteUsersRef = useRef([]);
  const remoteUsersIntervalRef = useRef(null);

  // Initialize call service for signaling first
  useEffect(() => {
    // Skip initialization if any required parameter is missing
    if (!userId || !rtmToken) {
      console.log("Skipping CallService initialization - missing required parameters");
      return;
    }

    console.log("Getting CallService instance for", userId);

    // Use singleton pattern through static method
    callServiceRef.current = CallService.getInstance(userId, rtmToken);
  }, [userId, rtmToken]);

  // Initialize video call service after call service is initialized
  useEffect(() => {
    // Skip initialization if any required parameter is missing
    if (!userId || !callServiceRef.current) {
      console.log("Skipping VideoCallService initialization - missing required parameters");
      return;
    }

    // Skip if rtcToken is not available yet
    if (!rtcToken) {
      console.log("Waiting for RTC token before initializing VideoCallService");
      return;
    }

    console.log("Getting VideoCallService instance for", userId);

    // Pass the CallService instance to VideoCallService for RTM signaling
    videoCallServiceRef.current = VideoCallService.getInstance(
      userId,
      rtcToken,
      callServiceRef.current
    );

    // Initialize the service
    videoCallServiceRef.current.initialize().catch(err => {
      console.error("Error initializing VideoCallService:", err);
      setError(`Failed to initialize video call service: ${err.message}`);
    });

    // Clean up function
    return () => {
      if (videoCallServiceRef.current) {
        videoCallServiceRef.current.cleanup().catch(err => {
          console.error("Error cleaning up VideoCallService:", err);
        });
      }
    };
  }, [userId, rtcToken]);

  // Add chat service reference for call activity tracking
  const chatServiceRef = useRef(null);

  // Set chat service from parent component
  const setChatService = useCallback((chatService) => {
    if (chatService && typeof chatService.sendCallActivityMessage === "function") {
      chatServiceRef.current = chatService;
      console.log("Chat service set for video call activity tracking");
    }
  }, []);

    // End a video call
  const endCall = useCallback(async () => {
    console.log("[END CALL] Attempting to end video call");

    if (!activeCall) {
      console.warn("[END CALL] No active video call to end");
      return false;
    }

    try {
      // Record call duration for history
      const duration = Math.floor((Date.now() - activeCall.startTime) / 1000);

      // Send call activity message via chat service
      if (chatServiceRef.current) {
        chatServiceRef.current
          .sendCallActivityMessage(activeCall.peerId, {
            type: "call_ended",
            duration: duration,
            callId: activeCall.callId,
            isOutgoing: activeCall.isOutgoing,
            isVideoCall: true, // Add flag to indicate this is a video call
            timestamp: Date.now(),
          })
          .catch((err) => console.error("Error sending video call ended activity:", err));
      }

      try {
        // First, try to signal call end via RTM with retry logic
        if (callServiceRef.current) {
          console.log(`[END CALL] Sending RTM end signal to ${activeCall.peerId}`);

          // Try multiple times to ensure the signal gets through
          let signalSent = false;
          for (let attempt = 1; attempt <= 3 && !signalSent; attempt++) {
            try {
              await callServiceRef.current.endCall(activeCall.callId, activeCall.peerId);
              console.log(`[END CALL] RTM end signal sent successfully on attempt ${attempt}`);
              signalSent = true;
            } catch (attemptErr) {
              console.warn(`[END CALL] RTM signal attempt ${attempt} failed:`, attemptErr);
              if (attempt < 3) {
                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 500));
              }
            }
          }

          if (!signalSent) {
            console.error("[END CALL] Failed to send RTM end signal after 3 attempts");
          }
        }
      } catch (signalErr) {
        console.error("[END CALL] Error signaling video call end:", signalErr);
        // Continue with cleanup even if signaling failed
      }

      // Leave the video call
      try {
        if (videoCallServiceRef.current) {
          console.log("[END CALL] Leaving video call channel");
          await videoCallServiceRef.current.leaveChannel();
          console.log("[END CALL] Successfully left video call channel");
        }
      } catch (err) {
        console.error("[END CALL] Error leaving video call channel:", err);
        // Continue with cleanup even if channel leave failed
      }

      // Reset state variables
      setCallStatus(CALL_STATUS.IDLE);
      setCallDuration(0);
      setLocalAudioTrack(null);
      setLocalVideoTrack(null);
      setRemoteUsers([]);

      // Update call history
      updateCallHistoryItem(activeCall.peerId, {
        duration,
        status: "ended",
      });

      // Reset call object last to avoid UI flicker
      setActiveCall(null);
      currentCallRef.current = null;

      console.log("[END CALL] Video call successfully ended and cleaned up");

      return true;
    } catch (err) {
      console.error("[END CALL] Error ending video call:", err);
      setError(`Failed to end video call: ${err.message}`);

      // Force cleanup even if there was an error
      setCallStatus(CALL_STATUS.IDLE);
      setCallDuration(0);
      setLocalAudioTrack(null);
      setLocalVideoTrack(null);
      setRemoteUsers([]);
      setActiveCall(null);
      currentCallRef.current = null;

      return false;
    }
  }, [activeCall]);

  // Set up event listeners for the call service
  useEffect(() => {
    // Skip if call service is not initialized
    if (!callServiceRef.current) return;

    console.log("[VIDEO CALL] Setting up call listeners for video calls");

    // Define the listener functions so we can reference them for cleanup
    const handleIncomingCall = (call) => {
      console.log("[SIGNAL] Incoming call received in video call handler:", call);
      console.log("[SIGNAL] Call type:", call.isVideoCall ? "Video" : "Audio");

      // Check if this is a video call
      if (call.isVideoCall) {
        console.log("[SIGNAL] Setting incoming video call state");
        setIncomingCall(call);

        // Note: Ringtone will be handled by VideoIncomingCallModal component
        // to avoid multiple audio instances and ensure proper cleanup
      } else {
        console.log("[SIGNAL] Ignoring audio call in video call handler");
      }
    };

    const handleCallAccepted = (message) => {
      console.log("[SIGNAL] Video call accepted for ID:", message.callId);
      if (activeCall && activeCall.callId === message.callId) {
        console.log("[SIGNAL] Video call signaling accepted, connecting...");
        connectToVideoCall(activeCall.channelName, activeCall.rtcToken);
      }
    };

    const handleCallRejected = (callId, reason) => {
      console.log("[SIGNAL] Video call rejected:", callId, reason);
      if (activeCall && activeCall.callId === callId) {
        setError(`Call was rejected${reason ? `: ${reason}` : ""}`);
        setCallStatus(CALL_STATUS.REJECTED);
      }
    };

    const handleCallEnded = (callId) => {
      console.log("[SIGNAL] Video call ended by peer:", callId);

      // Case 1: Handle active call being ended by remote
      if (activeCall && activeCall.callId === callId) {
        console.log("[SIGNAL] Active video call ended by remote peer");
        endCall();
      }
      // Case 2: Handle incoming call being canceled before answering
      else if (incomingCall && incomingCall.callId === callId) {
        console.log(`[CRITICAL] Caller canceled incoming video call ${callId} before it was answered`);

        // Send missed call activity message via chat service
        if (chatServiceRef.current) {
          chatServiceRef.current
            .sendCallActivityMessage(incomingCall.callerId, {
              type: "call_missed",
              callId: incomingCall.callId,
              isOutgoing: false,
              isVideoCall: true, // Add flag to indicate this is a video call
              timestamp: Date.now(),
            })
            .catch((err) => console.error("Error sending missed video call activity:", err));
        }

        // Add to call history as missed call
        addToCallHistory({
          peerId: incomingCall.callerId,
          peerName: incomingCall.callerName,
          timestamp: Date.now(),
          duration: 0,
          isOutgoing: false,
          isVideoCall: true,
          status: "caller canceled",
        });

        // Show a brief notification
        setError("Caller canceled the video call");

        // CRITICAL: Clear the incoming call state immediately to close the modal
        // This ensures the modal closes when the caller cancels
        console.log("[CRITICAL] Clearing incoming call state to close modal");
        setIncomingCall(null);
      }
      // Case 3: Handle any other call end scenarios
      else {
        console.log(`[SIGNAL] Received call end for unknown call ID: ${callId}`);
        console.log(`[SIGNAL] Current active call: ${activeCall?.callId || 'none'}`);
        console.log(`[SIGNAL] Current incoming call: ${incomingCall?.callId || 'none'}`);
      }
    };

    // Register the listeners using the proper methods
    callServiceRef.current.onIncomingCall(handleIncomingCall);
    callServiceRef.current.onCallAccepted(handleCallAccepted);
    callServiceRef.current.onCallRejected(handleCallRejected);
    callServiceRef.current.onCallEnded(handleCallEnded);

    console.log("[VIDEO CALL] Successfully registered all video call listeners");

    // Clean up function
    return () => {
      console.log("[VIDEO CALL] Cleaning up video call listeners");

      // Properly unregister all listeners
      if (callServiceRef.current) {
        callServiceRef.current.offIncomingCall(handleIncomingCall);
        callServiceRef.current.offCallAccepted(handleCallAccepted);
        callServiceRef.current.offCallRejected(handleCallRejected);
        callServiceRef.current.offCallEnded(handleCallEnded);
        console.log("[VIDEO CALL] Successfully unregistered all video call listeners");
      }

      // Terminate any active call on unmount
      if (activeCall) {
        setCallStatus(CALL_STATUS.IDLE);
        setActiveCall(null);
      }
    };
  }, [userId, rtmToken, activeCall, endCall]);

  // Function to update remote users - defined at the top level
  const updateRemoteUsers = useCallback(() => {
    if (!videoCallServiceRef.current) return;

    try {
      // Get the current remote users from the service
      const currentRemoteUsers = [...videoCallServiceRef.current.remoteUsers];

      // Skip update if nothing has changed
      if (remoteUsersRef.current.length === currentRemoteUsers.length) {
        // Check if the users are the same (by uid) and have the same video/audio status
        const noChanges = remoteUsersRef.current.every((refUser, index) => {
          const currentUser = currentRemoteUsers[index];
          if (!currentUser || refUser.uid !== currentUser.uid) return false;

          const currentHasVideo = videoCallServiceRef.current.videoAvailabilityMap.get(currentUser.uid) || false;
          const refHasVideo = refUser.hasVideo;
          const currentHasAudio = !!currentUser.audioTrack;
          const refHasAudio = refUser.hasAudio;

          return currentHasVideo === refHasVideo && currentHasAudio === refHasAudio;
        });

        if (noChanges) return; // Skip update if nothing changed
      }

      // Create a new array with plain objects to avoid recursion issues
      const safeRemoteUsers = currentRemoteUsers.map(user => {
        // Create a plain object with the properties we need
        const hasVideoValue = videoCallServiceRef.current.videoAvailabilityMap.get(user.uid) || false;

        return {
          uid: user.uid,
          audioTrack: user.audioTrack,
          videoTrack: user.videoTrack,
          hasAudio: !!user.audioTrack,
          hasVideo: hasVideoValue
        };
      });

      // Update our ref first
      remoteUsersRef.current = safeRemoteUsers;

      // Then update state (only if needed)
      setRemoteUsers(safeRemoteUsers);
    } catch (err) {
      console.error("Error updating remote users:", err);
    }
  }, []);

  // Effect to set up event listeners for remote user updates
  useEffect(() => {
    // Only set up listeners if we're in a connected call and have a video service
    if (callStatus !== CALL_STATUS.CONNECTED || !videoCallServiceRef.current || !videoCallServiceRef.current.client) {
      return;
    }

    console.log("Setting up remote user update event listeners");

    const handleUserPublished = () => {
      // Small delay to ensure the track is fully processed
      setTimeout(updateRemoteUsers, 100);
    };

    const handleUserUnpublished = () => {
      setTimeout(updateRemoteUsers, 100);
    };

    const handleUserLeft = () => {
      setTimeout(updateRemoteUsers, 100);
    };

    // Add event listeners to the client
    const client = videoCallServiceRef.current.client;
    client.on("user-published", handleUserPublished);
    client.on("user-unpublished", handleUserUnpublished);
    client.on("user-left", handleUserLeft);

    // Start the interval for regular updates
    remoteUsersIntervalRef.current = setInterval(updateRemoteUsers, 3000);

    // Initial update
    updateRemoteUsers();

    return () => {
      // Clean up event listeners
      if (client) {
        client.off("user-published", handleUserPublished);
        client.off("user-unpublished", handleUserUnpublished);
        client.off("user-left", handleUserLeft);
      }

      // Clear the interval
      if (remoteUsersIntervalRef.current) {
        clearInterval(remoteUsersIntervalRef.current);
        remoteUsersIntervalRef.current = null;
      }
    };
  }, [callStatus, updateRemoteUsers]);

  // Set up call timer when in active call
  useEffect(() => {
    if (callStatus === CALL_STATUS.CONNECTED) {
      setCallDuration(0);
      callTimerRef.current = setInterval(() => {
        setCallDuration((prev) => prev + 1);
      }, 1000);
    } else if (callStatus !== CALL_STATUS.CONNECTED && callTimerRef.current) {
      clearInterval(callTimerRef.current);
      callTimerRef.current = null;
    }

    return () => {
      if (callTimerRef.current) {
        clearInterval(callTimerRef.current);
      }
    };
  }, [callStatus]);

  // Effect to handle RTC token changes during active calls
  useEffect(() => {
    const handleTokenRenewal = async () => {
      if (rtcToken && videoCallServiceRef.current && callStatus === CALL_STATUS.CONNECTED) {
        console.log("RTC token updated during active call, attempting token renewal");

        try {
          // Use the video service's renewToken method to update without disconnecting
          const renewalSuccess = await videoCallServiceRef.current.renewToken(rtcToken);

          if (renewalSuccess) {
            console.log("Successfully renewed RTC token during active video call");
          } else {
            console.warn("Failed to renew RTC token during active video call");
          }
        } catch (error) {
          console.error("Error renewing RTC token during active video call:", error);
        }
      } else if (rtcToken && videoCallServiceRef.current) {
        console.log("RTC token updated, updating video call service");
        // Update the video call service with the new token for non-active calls
        videoCallServiceRef.current.rtcToken = rtcToken;
      }
    };

    handleTokenRenewal();
  }, [rtcToken, callStatus]);

  // Connect to video call - refactored to not use hooks inside
  const connectToVideoCall = async (channelName, channelSpecificToken = null) => {
    try {
      if (!videoCallServiceRef.current) {
        throw new Error("Video call service not initialized");
      }

      // Use channel-specific token if provided, otherwise fall back to generic token
      const tokenToUse = channelSpecificToken || rtcToken;
      if (!tokenToUse) {
        throw new Error("RTC token is missing or invalid");
      }

      setCallStatus(CALL_STATUS.CONNECTING);

      console.log(`Connecting to video call with ${channelSpecificToken ? 'channel-specific' : 'generic'} token`);

      // Join the channel
      const { localAudioTrack, localVideoTrack } = await videoCallServiceRef.current.joinChannel(channelName, tokenToUse);

      // Set local tracks
      setLocalAudioTrack(localAudioTrack);
      setLocalVideoTrack(localVideoTrack);

      // Set call status to connected - this will trigger the useEffect above to set up remote user handling
      setCallStatus(CALL_STATUS.CONNECTED);

      return true;
    } catch (error) {
      console.error("Error connecting to video call:", error);
      setError(`Failed to connect to video call: ${error.message}`);
      setCallStatus(CALL_STATUS.DISCONNECTED);
      return false;
    }
  };

  // Start a video call
  const startCall = useCallback(async (recipientId, recipientName) => {
    if (!userId || !rtcToken) {
      setError("Cannot start call: Missing user ID or token");
      return false;
    }

    if (!recipientId) {
      setError("Cannot start call: Missing recipient ID");
      return false;
    }

    try {
      console.log(`Starting cross-platform video call to ${recipientName} (${recipientId})`);

      setCallStatus(CALL_STATUS.CONNECTING);
      setError(null);

      // Generate a unique, Agora-compliant channel name for the call
      const channelName = generateChannelName(userId, recipientId, 'video');
      const callId = `video_call_${Date.now()}`;

      // Validate the generated channel name
      const validation = validateChannelName(channelName);
      if (!validation.isValid) {
        console.error("Generated invalid channel name:", validation.error);
        setError(`Failed to create video call: ${validation.error}`);
        return false;
      }

      console.log(`Generated video channel name: ${channelName} (${channelName.length} bytes)`);

      // Get event code from stored event data
      const eventData = JSON.parse(localStorage.getItem("eventData") || "{}");
      const eventCode = eventData.eventCode;

      if (!eventCode) {
        console.error("Event code not found in stored event data");
        setError("Cannot start video call: Event code not available");
        return false;
      }

      // Use cross-platform call service to initiate video call
      let callDetails;
      try {
        console.log("Initiating cross-platform video call...");
        callDetails = await crossPlatformCallService.initiateCall({
          callerId: user?.agoraid, // Use users.id instead of agoraId
          calleeId: recipientId, // This should also be users.id
          eventCode: eventCode,
          channelName: channelName,
          isVideoCall: true,
          callerName: user?.fullname || "Unknown",
          callerJobTitle: user?.jobtitle || "",
          callerCompany: user?.company || ""
        });

        console.log("Cross-platform video call initiated successfully:", callDetails);
      } catch (crossPlatformError) {
        console.error("Cross-platform video call initiation failed:", crossPlatformError);

        // Fallback to original token validation method
        console.log("Falling back to original token validation...");
        try {
          const tokenResult = await checkTokensBeforeCall(userId, channelName);
          callDetails = {
            success: true,
            rtcToken: tokenResult.rtcToken,
            channelName: channelName,
            callId: callId
          };
          console.log(`Fallback token validation successful, using ${tokenResult.isChannelSpecific ? 'channel-specific' : 'generic'} RTC token`);
        } catch (tokenError) {
          console.error("Fallback token validation failed:", tokenError);
          setError(`Cannot start video call: ${tokenError.message}`);
          return false;
        }
      }

      // Create active call object with RTC token
      const newCall = {
        callId,
        channelName,
        peerId: recipientId,
        peerName: recipientName,
        startTime: Date.now(),
        isOutgoing: true,
        isVideoCall: true, // Flag to indicate this is a video call
        rtcToken: callDetails.rtcToken, // Store RTC token for later use
      };

      setActiveCall(newCall);
      currentCallRef.current = newCall;

      // Send call request to recipient through signaling
      await callServiceRef.current.initiateCall({
        callId,
        channelName,
        recipientId,
        callerId: userId,
        callerName: user?.fullname || "Unknown",
        timestamp: Date.now(),
        isVideoCall: true, // Flag to indicate this is a video call
      });

      // Send call activity message via chat service
      if (chatServiceRef.current) {
        chatServiceRef.current
          .sendCallActivityMessage(recipientId, {
            type: "call_started",
            callId: callId,
            isOutgoing: true,
            isVideoCall: true, // Add flag to indicate this is a video call
            timestamp: Date.now(),
          })
          .catch((err) => console.error("Error sending video call started activity:", err));
      }

      // Add to call history
      addToCallHistory({
        peerId: recipientId,
        peerName: recipientName,
        timestamp: Date.now(),
        duration: 0,
        isOutgoing: true,
        isVideoCall: true,
        status: "connecting",
      });

      return true;
    } catch (err) {
      console.error("Error starting video call:", err);
      setError(`Failed to start video call: ${err.message}`);
      setCallStatus(CALL_STATUS.IDLE);
      setActiveCall(null);
      return false;
    }
  }, [userId, rtcToken, user?.fullname]);

  // Cancel an outgoing video call (before it's answered)
  const cancelCall = useCallback(async () => {
    if (!activeCall || !activeCall.isOutgoing) {
      console.warn("[CANCEL CALL] No outgoing video call to cancel");
      return false;
    }

    try {
      console.log(`[CANCEL CALL] Canceling outgoing video call to ${activeCall.peerName}`);

      // Send cancellation signal via RTM with retry logic
      if (callServiceRef.current) {
        console.log(`[CANCEL CALL] Sending RTM cancellation signal to ${activeCall.peerId}`);

        let signalSent = false;
        for (let attempt = 1; attempt <= 3 && !signalSent; attempt++) {
          try {
            await callServiceRef.current.endCall(activeCall.callId, activeCall.peerId);
            console.log(`[CANCEL CALL] RTM cancellation signal sent successfully on attempt ${attempt}`);
            signalSent = true;
          } catch (attemptErr) {
            console.warn(`[CANCEL CALL] RTM signal attempt ${attempt} failed:`, attemptErr);
            if (attempt < 3) {
              await new Promise(resolve => setTimeout(resolve, 500));
            }
          }
        }
      }

      // Send call activity message via chat service
      if (chatServiceRef.current) {
        chatServiceRef.current
          .sendCallActivityMessage(activeCall.peerId, {
            type: "call_cancelled",
            callId: activeCall.callId,
            isOutgoing: true,
            isVideoCall: true,
            timestamp: Date.now(),
          })
          .catch((err) => console.error("Error sending video call cancelled activity:", err));
      }

      // Update call history
      updateCallHistoryItem(activeCall.peerId, {
        status: "cancelled",
        duration: 0,
      });

      // Reset state
      setCallStatus(CALL_STATUS.IDLE);
      setActiveCall(null);
      currentCallRef.current = null;

      console.log("[CANCEL CALL] Video call successfully cancelled");
      return true;
    } catch (error) {
      console.error("[CANCEL CALL] Error canceling video call:", error);
      setError(`Failed to cancel video call: ${error.message}`);
      return false;
    }
  }, [activeCall]);

  // Answer a video call
  const answerCall = async () => {
    try {
      if (!incomingCall) {
        console.error("[CRITICAL] No incoming video call to answer");
        return false;
      }

      console.log(`[CRITICAL] Answering video call ${incomingCall.callId} from ${incomingCall.callerId}`);

      // Set active call information
      const newCall = {
        callId: incomingCall.callId,
        channelName: incomingCall.channelName,
        peerId: incomingCall.callerId,
        peerName: incomingCall.callerName,
        startTime: Date.now(),
        isOutgoing: false,
        isVideoCall: true,
      };

      setActiveCall(newCall);
      currentCallRef.current = newCall;

      // Set call status to connecting
      setCallStatus(CALL_STATUS.CONNECTING);

      // Accept the call
      await callServiceRef.current.acceptCall(incomingCall.callId, incomingCall.callerId);

      console.log(`[CRITICAL] Successfully sent acceptance for video call ${incomingCall.callId}`);

      // Fetch channel-specific RTC token for the incoming call
      let channelRtcToken;
      try {
        console.log("Fetching channel-specific RTC token for incoming video call...");
        const tokenResult = await checkTokensBeforeCall(userId, incomingCall.channelName);
        channelRtcToken = tokenResult.rtcToken;
        console.log(`Token validation successful for incoming call, using ${tokenResult.isChannelSpecific ? 'channel-specific' : 'generic'} RTC token`);
      } catch (tokenError) {
        console.error("Token validation failed for incoming video call:", tokenError);
        setError(`Cannot answer video call: ${tokenError.message}`);
        setCallStatus(CALL_STATUS.IDLE);
        return false;
      }

      // Send call activity message via chat service
      if (chatServiceRef.current) {
        chatServiceRef.current
          .sendCallActivityMessage(incomingCall.callerId, {
            type: "call_answered",
            callId: incomingCall.callId,
            isOutgoing: false,
            isVideoCall: true, // Add flag to indicate this is a video call
            timestamp: Date.now(),
          })
          .catch((err) => console.error("Error sending video call answered activity:", err));
      }

      // Connect to the video call with channel-specific token
      await connectToVideoCall(incomingCall.channelName, channelRtcToken);

      // Clear the incoming call reference
      setIncomingCall(null);

      return true;
    } catch (error) {
      console.error("[CRITICAL] Error answering video call:", error);
      setCallStatus(CALL_STATUS.IDLE);
      return false;
    }
  };

  // Reject a video call
  const rejectCall = useCallback(async () => {
    if (!incomingCall) {
      return false;
    }

    try {
      console.log("Rejecting video call from:", incomingCall.callerName);
      await callServiceRef.current.rejectCall(
        incomingCall.callId,
        incomingCall.callerId
      );

      // Send call activity message via chat service
      if (chatServiceRef.current) {
        chatServiceRef.current
          .sendCallActivityMessage(incomingCall.callerId, {
            type: "call_rejected",
            callId: incomingCall.callId,
            isOutgoing: false,
            isVideoCall: true, // Add flag to indicate this is a video call
            timestamp: Date.now(),
          })
          .catch((err) => console.error("Error sending video call rejected activity:", err));
      }

      // Add to call history
      addToCallHistory({
        peerId: incomingCall.callerId,
        peerName: incomingCall.callerName,
        timestamp: Date.now(),
        duration: 0,
        isOutgoing: false,
        isVideoCall: true,
        status: "rejected",
      });

      setIncomingCall(null);
      return true;
    } catch (err) {
      console.error("Error rejecting video call:", err);
      setError(`Failed to reject video call: ${err.message}`);
      setIncomingCall(null);
      return false;
    }
  }, [incomingCall]);

  // Toggle video on/off
  const toggleVideo = useCallback(async () => {
    try {
      if (!videoCallServiceRef.current) {
        throw new Error("Video call service not initialized");
      }

      const newState = !isVideoEnabled;
      await videoCallServiceRef.current.toggleVideo(newState);
      setIsVideoEnabled(newState);
      return newState;
    } catch (error) {
      console.error("Error toggling video:", error);
      setError(`Failed to toggle video: ${error.message}`);
      return isVideoEnabled;
    }
  }, [isVideoEnabled]);

  // Toggle audio on/off
  const toggleAudio = useCallback(async () => {
    try {
      if (!videoCallServiceRef.current) {
        throw new Error("Video call service not initialized");
      }

      const newState = !isAudioEnabled;
      await videoCallServiceRef.current.toggleAudio(newState);
      setIsAudioEnabled(newState);
      return newState;
    } catch (error) {
      console.error("Error toggling audio:", error);
      setError(`Failed to toggle audio: ${error.message}`);
      return isAudioEnabled;
    }
  }, [isAudioEnabled]);

  // Helper function to add entry to call history
  const addToCallHistory = (callData) => {
    setCallHistory((prev) => [callData, ...prev.slice(0, 49)]); // Keep only last 50 calls
  };

  // Helper function to update an existing call history item
  const updateCallHistoryItem = (peerId, updates) => {
    setCallHistory((prev) =>
      prev.map((call) =>
        call.peerId === peerId &&
        call.timestamp === (activeCall?.startTime || 0)
          ? { ...call, ...updates }
          : call
      )
    );
  };

  // Clear any error that was set
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Format duration in MM:SS format
  const formatCallDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, "0")}:${secs
      .toString()
      .padStart(2, "0")}`;
  };

  return {
    callStatus,
    error,
    activeCall,
    incomingCall,
    localVideoTrack,
    localAudioTrack,
    remoteUsers,
    isVideoEnabled,
    isAudioEnabled,
    callDuration,
    callHistory,
    formatCallDuration,
    startCall,
    answerCall,
    rejectCall,
    endCall,
    cancelCall, // Add the cancelCall function
    toggleVideo,
    toggleAudio,
    clearError,
    setChatService, // Add the setChatService function to the return object
  };
};

export default useVideoCall;
