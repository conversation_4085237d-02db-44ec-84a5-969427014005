# Cross-Platform Calling Testing Guide

This guide provides comprehensive testing procedures for the cross-platform calling functionality.

## Testing Overview

The cross-platform calling system needs to be tested across multiple scenarios:
1. **Web-to-Web calls** (existing functionality)
2. **Web-to-Mobile calls** (new functionality)
3. **Mobile-to-Web calls** (new functionality)
4. **Call state synchronization**
5. **Error handling and edge cases**

## Prerequisites for Testing

### Required Setup
1. **Firebase Project**: Properly configured with FCM
2. **Backend API**: calls/send-call endpoint implemented
3. **Mobile Apps**: Flutter apps with Agora and FCM integration
4. **Test Devices**: At least 2 devices (web browsers and/or mobile devices)

### Test Environment
1. **Development Environment**: Local development server
2. **Network Connectivity**: Stable internet connection
3. **Browser Permissions**: Notification permissions enabled
4. **Mobile Devices**: Physical devices recommended for FCM testing

## Testing Procedures

### 1. Basic Functionality Tests

#### Test 1.1: Firebase Configuration
```javascript
// Check Firebase configuration
import { validateFirebaseConfig } from './src/config/firebase-config';
console.log('Firebase config valid:', validateFirebaseConfig());
```

**Expected Result**: Should return `true` with no console warnings

#### Test 1.2: FCM Token Generation
```javascript
// Test FCM token generation
import { firebaseService } from './src/services/FirebaseService';
const token = await firebaseService.requestPermissionAndGetToken();
console.log('FCM Token:', token);
```

**Expected Result**: Should return a valid FCM token string

#### Test 1.3: Cross-Platform Service Initialization
```javascript
// Test service initialization
import { crossPlatformCallManager } from './src/services/CrossPlatformCallManager';
await crossPlatformCallManager.initialize();
console.log('Manager initialized:', crossPlatformCallManager.isInitialized);
```

**Expected Result**: Should initialize without errors

### 2. Web-to-Web Call Testing

#### Test 2.1: Voice Call (Regression Test)
1. Open two browser windows/tabs
2. Login with different users
3. Initiate voice call from User A to User B
4. Accept call on User B
5. Verify audio connection
6. End call

**Expected Result**: Should work exactly as before (no regression)

#### Test 2.2: Video Call (Regression Test)
1. Open two browser windows/tabs
2. Login with different users
3. Initiate video call from User A to User B
4. Accept call on User B
5. Verify audio and video connection
6. End call

**Expected Result**: Should work exactly as before (no regression)

### 3. Web-to-Mobile Call Testing

#### Test 3.1: Web-to-Mobile Voice Call
1. **Setup**: Web browser (User A) and Mobile device (User B)
2. **Action**: User A initiates voice call to User B
3. **Verify**: 
   - FCM notification received on mobile
   - Mobile app shows incoming call UI
   - Call can be accepted/rejected from mobile
   - Audio connection established when accepted

#### Test 3.2: Web-to-Mobile Video Call
1. **Setup**: Web browser (User A) and Mobile device (User B)
2. **Action**: User A initiates video call to User B
3. **Verify**:
   - FCM notification received on mobile
   - Mobile app shows incoming video call UI
   - Call can be accepted/rejected from mobile
   - Audio and video connection established when accepted

### 4. Mobile-to-Web Call Testing

#### Test 4.1: Mobile-to-Web Voice Call
1. **Setup**: Mobile device (User A) and Web browser (User B)
2. **Action**: User A initiates voice call to User B from mobile
3. **Verify**:
   - Web browser receives call notification
   - Incoming call modal appears
   - Call can be accepted/rejected from web
   - Audio connection established when accepted

#### Test 4.2: Mobile-to-Web Video Call
1. **Setup**: Mobile device (User A) and Web browser (User B)
2. **Action**: User A initiates video call to User B from mobile
3. **Verify**:
   - Web browser receives call notification
   - Incoming video call modal appears
   - Call can be accepted/rejected from web
   - Audio and video connection established when accepted

### 5. Call State Synchronization Testing

#### Test 5.1: Call Cancellation Sync
1. **Setup**: User A (web) calls User B (mobile)
2. **Action**: User A cancels call before User B answers
3. **Verify**: 
   - Mobile device receives cancellation notification
   - Incoming call UI disappears on mobile
   - Call state properly cleaned up

#### Test 5.2: Call Rejection Sync
1. **Setup**: User A (web) calls User B (mobile)
2. **Action**: User B rejects call
3. **Verify**:
   - Web browser receives rejection notification
   - Call UI updates to show rejection
   - Call state properly cleaned up

#### Test 5.3: Call Acceptance Sync
1. **Setup**: User A (web) calls User B (mobile)
2. **Action**: User B accepts call
3. **Verify**:
   - Web browser receives acceptance notification
   - Call UI transitions to active call state
   - Media connection established

### 6. Error Handling Tests

#### Test 6.1: Network Failure During Call
1. **Setup**: Active call between web and mobile
2. **Action**: Disconnect network on one device
3. **Verify**: 
   - Call ends gracefully
   - UI updates appropriately
   - No hanging states

#### Test 6.2: Invalid Token Handling
1. **Setup**: Expired or invalid RTC token
2. **Action**: Attempt to initiate call
3. **Verify**:
   - Error handled gracefully
   - User receives appropriate error message
   - Fallback token mechanism works

#### Test 6.3: FCM Delivery Failure
1. **Setup**: Mobile device with FCM disabled
2. **Action**: Web user calls mobile user
3. **Verify**:
   - Call still works if mobile app is open
   - Appropriate error handling if app is closed
   - No crashes or hanging states

### 7. Performance Testing

#### Test 7.1: Multiple Simultaneous Calls
1. **Setup**: Multiple users making calls simultaneously
2. **Action**: Initiate several calls at once
3. **Verify**:
   - All calls process correctly
   - No resource conflicts
   - Performance remains acceptable

#### Test 7.2: Call Duration Test
1. **Setup**: Long-duration call (30+ minutes)
2. **Action**: Maintain active call
3. **Verify**:
   - Call remains stable
   - No memory leaks
   - Token refresh works if needed

### 8. Edge Case Testing

#### Test 8.1: Rapid Call Actions
1. **Action**: Quickly initiate and cancel multiple calls
2. **Verify**: No race conditions or hanging states

#### Test 8.2: Browser Refresh During Call
1. **Setup**: Active call in web browser
2. **Action**: Refresh browser page
3. **Verify**: Call state properly restored or cleaned up

#### Test 8.3: App Background/Foreground
1. **Setup**: Active call on mobile
2. **Action**: Switch mobile app to background/foreground
3. **Verify**: Call continues without interruption

## Automated Testing

### Unit Tests
```javascript
// Example unit test for cross-platform service
describe('CrossPlatformCallService', () => {
  test('should initiate call with valid parameters', async () => {
    const result = await crossPlatformCallService.initiateCall(mockCallData);
    expect(result.success).toBe(true);
    expect(result.callId).toBeDefined();
  });
});
```

### Integration Tests
```javascript
// Example integration test
describe('Cross-Platform Call Flow', () => {
  test('should handle complete call lifecycle', async () => {
    // Test call initiation, acceptance, and termination
  });
});
```

## Test Data and Mock Objects

### Mock Call Data
```javascript
const mockCallData = {
  callerId: 'test_user_123',
  calleeId: 'test_user_456',
  eventCode: 'TEST_EVENT_2024',
  channelName: 'test_channel_' + Date.now(),
  isVideoCall: false,
  callerName: 'Test User'
};
```

### Mock FCM Response
```javascript
const mockFCMResponse = {
  success: true,
  callId: 'call_123456',
  callerFCMToken: 'mock_caller_token',
  calleeFCMToken: 'mock_callee_token',
  rtcToken: 'mock_rtc_token',
  jwtToken: 'mock_jwt_token'
};
```

## Debugging and Troubleshooting

### Debug Logging
Enable debug mode by setting:
```javascript
localStorage.setItem('crossPlatformCallDebug', 'true');
```

### Common Issues and Solutions

1. **FCM Notifications Not Received**
   - Check browser notification permissions
   - Verify Firebase configuration
   - Test with Firebase Console

2. **Call State Sync Issues**
   - Check network connectivity
   - Verify backend API responses
   - Monitor console logs

3. **Audio/Video Issues**
   - Check device permissions
   - Verify Agora token validity
   - Test with different browsers/devices

### Monitoring Tools

1. **Browser DevTools**: Network tab, Console logs
2. **Firebase Console**: FCM message delivery status
3. **Agora Console**: RTC usage and quality metrics
4. **Backend Logs**: API call success/failure rates

## Test Reporting

### Test Results Template
```
Test: [Test Name]
Date: [Date/Time]
Environment: [Development/Staging/Production]
Devices: [Browser/Mobile details]
Result: [PASS/FAIL]
Notes: [Additional observations]
Issues: [Any problems encountered]
```

### Success Criteria

A test is considered successful if:
1. ✅ No console errors
2. ✅ Expected functionality works
3. ✅ UI updates appropriately
4. ✅ Call state synchronizes correctly
5. ✅ No memory leaks or performance issues

## Continuous Testing

### Automated Test Schedule
- **Daily**: Basic functionality tests
- **Weekly**: Full regression testing
- **Before Release**: Complete test suite

### Test Environment Maintenance
- Keep Firebase configuration updated
- Maintain test user accounts
- Update mobile app versions
- Monitor API endpoint availability

## Conclusion

Thorough testing of the cross-platform calling functionality ensures:
- Reliable communication between web and mobile platforms
- Proper error handling and edge case management
- Optimal user experience across all devices
- Maintainable and scalable codebase

Follow this guide systematically to validate all aspects of the cross-platform calling implementation.
