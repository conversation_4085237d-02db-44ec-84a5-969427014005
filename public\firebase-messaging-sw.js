// Import Firebase scripts for service worker
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.0.0/firebase-messaging-compat.js');

// Firebase configuration
// Note: In a service worker, we can't import ES6 modules, so we need to define the config here
// Make sure this matches your Firebase project configuration
const firebaseConfig = {
  apiKey: "AIzaSyAIkEyJ_S8vToYMO2b4UBFTTdb6fOpSSdQ",
  authDomain: "cmt-meet-fd921.firebaseapp.com",
  projectId: "cmt-meet-fd921",
  storageBucket: "cmt-meet-fd921.firebasestorage.app",
  messagingSenderId: "953784194836",
  appId: "1:953784194836:web:9fbc8a15ba903d49c51401"
};

// Initialize Firebase in service worker
firebase.initializeApp(firebaseConfig);

// Retrieve Firebase Messaging object
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage(function(payload) {
  console.log('[firebase-messaging-sw.js] Received background message:', payload);

  // Customize notification here
  const notificationTitle = payload.notification?.title || 'Incoming Call';
  const notificationOptions = {
    body: payload.notification?.body || 'You have an incoming call',
    icon: payload.notification?.icon || '/logo192.png',
    badge: '/logo192.png',
    tag: 'call-notification',
    requireInteraction: true, // Keep notification visible until user interacts
    actions: [
      {
        action: 'answer',
        title: 'Answer',
        icon: '/logo192.png'
      },
      {
        action: 'decline',
        title: 'Decline',
        icon: '/logo192.png'
      }
    ],
    data: payload.data // Pass through call data
  };

  // Show notification
  return self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click events
self.addEventListener('notificationclick', function(event) {
  console.log('[firebase-messaging-sw.js] Notification click received:', event);

  event.notification.close();

  const action = event.action;
  const callData = event.notification.data;

  if (action === 'answer') {
    // Handle answer action
    console.log('User clicked Answer');
    
    // Open the app and pass call data
    event.waitUntil(
      clients.openWindow(`/?action=answer&callId=${callData.callId}&callerId=${callData.callerId}&channelName=${callData.channelName}&isVideoCall=${callData.isVideoCall}`)
    );
  } else if (action === 'decline') {
    // Handle decline action
    console.log('User clicked Decline');
    
    // Send decline message back to the app
    event.waitUntil(
      clients.matchAll().then(function(clientList) {
        if (clientList.length > 0) {
          // Send message to existing client
          clientList[0].postMessage({
            type: 'call_declined',
            callId: callData.callId,
            callerId: callData.callerId
          });
        }
      })
    );
  } else {
    // Default click action - open the app
    event.waitUntil(
      clients.openWindow(`/?callId=${callData.callId}&callerId=${callData.callerId}&channelName=${callData.channelName}&isVideoCall=${callData.isVideoCall}`)
    );
  }
});
