import { crossPlatformCallService } from './CrossPlatformCallService';
import { firebaseService } from './FirebaseService';

/**
 * Cross-Platform Call Manager
 * Manages call state synchronization between web and mobile platforms
 * Handles FCM notifications and call lifecycle management
 */
export class CrossPlatformCallManager {
  constructor() {
    this.callStateListeners = new Map();
    this.activeCallStates = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize the cross-platform call manager
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      // Initialize Firebase service and request FCM token
      await firebaseService.requestPermissionAndGetToken();

      // Set up foreground message listener for incoming call notifications
      firebaseService.setupForegroundMessageListener((callData) => {
        this.handleIncomingCallNotification(callData);
      });

      // Set up service worker message listener for background notifications
      this.setupServiceWorkerMessageListener();

      // Handle URL parameters for call actions (when app is opened from notification)
      this.handleUrlCallActions();

      this.isInitialized = true;
      console.log('Cross-platform call manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize cross-platform call manager:', error);
    }
  }

  /**
   * Handle incoming call notification from FCM
   * @param {Object} callData - Call information from FCM
   */
  handleIncomingCallNotification(callData) {
    console.log('Handling incoming call notification:', callData);

    // Update call state
    this.updateCallState(callData.callId, {
      status: 'incoming',
      callData: callData,
      timestamp: Date.now()
    });

    // Notify listeners (e.g., UI components)
    this.notifyCallStateListeners(callData.callId, 'incoming', callData);
  }

  /**
   * Set up service worker message listener
   */
  setupServiceWorkerMessageListener() {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.addEventListener('message', (event) => {
        const { type, callId, callerId } = event.data;

        if (type === 'call_declined') {
          console.log('Call declined from service worker:', callId);
          this.handleCallDeclined(callId, callerId);
        }
      });
    }
  }

  /**
   * Handle URL parameters for call actions
   */
  handleUrlCallActions() {
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get('action');
    const callId = urlParams.get('callId');
    const callerId = urlParams.get('callerId');
    const channelName = urlParams.get('channelName');
    const isVideoCall = urlParams.get('isVideoCall') === 'true';

    if (action && callId) {
      console.log('Handling URL call action:', { action, callId, callerId, channelName, isVideoCall });

      if (action === 'answer') {
        this.handleCallAnsweredFromNotification({
          callId,
          callerId,
          channelName,
          isVideoCall
        });
      }

      // Clean up URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }

  /**
   * Handle call answered from notification
   * @param {Object} callInfo - Call information
   */
  async handleCallAnsweredFromNotification(callInfo) {
    try {
      console.log('Handling call answered from notification:', callInfo);

      // Update call state
      this.updateCallState(callInfo.callId, {
        status: 'accepted',
        timestamp: Date.now()
      });

      // Notify listeners to trigger call UI
      this.notifyCallStateListeners(callInfo.callId, 'accepted', callInfo);

    } catch (error) {
      console.error('Error handling call answered from notification:', error);
    }
  }

  /**
   * Handle call declined
   * @param {string} callId - Call identifier
   * @param {string} callerId - Caller identifier
   */
  async handleCallDeclined(callId, callerId) {
    try {
      console.log('Handling call declined:', callId);

      // Update call state
      this.updateCallState(callId, {
        status: 'declined',
        timestamp: Date.now()
      });

      // Send decline message to caller via backend
      await crossPlatformCallService.handleCallRejected(callId, callerId);

      // Notify listeners
      this.notifyCallStateListeners(callId, 'declined', { callId, callerId });

    } catch (error) {
      console.error('Error handling call declined:', error);
    }
  }

  /**
   * Update call state
   * @param {string} callId - Call identifier
   * @param {Object} stateUpdate - State update object
   */
  updateCallState(callId, stateUpdate) {
    const currentState = this.activeCallStates.get(callId) || {};
    const newState = { ...currentState, ...stateUpdate };
    this.activeCallStates.set(callId, newState);

    console.log(`Call state updated for ${callId}:`, newState);
  }

  /**
   * Get call state
   * @param {string} callId - Call identifier
   * @returns {Object|null} Call state or null if not found
   */
  getCallState(callId) {
    return this.activeCallStates.get(callId) || null;
  }

  /**
   * Register call state listener
   * @param {string} callId - Call identifier
   * @param {Function} listener - Listener function
   */
  addCallStateListener(callId, listener) {
    if (!this.callStateListeners.has(callId)) {
      this.callStateListeners.set(callId, []);
    }
    this.callStateListeners.get(callId).push(listener);
  }

  /**
   * Remove call state listener
   * @param {string} callId - Call identifier
   * @param {Function} listener - Listener function to remove
   */
  removeCallStateListener(callId, listener) {
    const listeners = this.callStateListeners.get(callId);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
      if (listeners.length === 0) {
        this.callStateListeners.delete(callId);
      }
    }
  }

  /**
   * Notify call state listeners
   * @param {string} callId - Call identifier
   * @param {string} status - Call status
   * @param {Object} data - Additional data
   */
  notifyCallStateListeners(callId, status, data) {
    const listeners = this.callStateListeners.get(callId);
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener(status, data);
        } catch (error) {
          console.error('Error in call state listener:', error);
        }
      });
    }
  }

  /**
   * Clean up call state
   * @param {string} callId - Call identifier
   */
  cleanupCallState(callId) {
    this.activeCallStates.delete(callId);
    this.callStateListeners.delete(callId);
    console.log(`Cleaned up call state for ${callId}`);
  }

  /**
   * Get all active call states
   * @returns {Map} Map of active call states
   */
  getAllCallStates() {
    return new Map(this.activeCallStates);
  }

  /**
   * Clear all call states
   */
  clearAllCallStates() {
    this.activeCallStates.clear();
    this.callStateListeners.clear();
    console.log('Cleared all call states');
  }
}

// Export singleton instance
export const crossPlatformCallManager = new CrossPlatformCallManager();
