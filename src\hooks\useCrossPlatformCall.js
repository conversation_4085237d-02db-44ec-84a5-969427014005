import { useState, useEffect, useCallback } from 'react';
import { crossPlatformCallManager } from '../services/CrossPlatformCallManager';
import { crossPlatformCallService } from '../services/CrossPlatformCallService';

/**
 * Custom hook for cross-platform calling functionality
 * Integrates with existing call hooks to provide cross-platform support
 */
export const useCrossPlatformCall = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [incomingCalls, setIncomingCalls] = useState(new Map());
  const [callStates, setCallStates] = useState(new Map());

  // Initialize cross-platform call manager
  useEffect(() => {
    const initializeManager = async () => {
      try {
        await crossPlatformCallManager.initialize();
        setIsInitialized(true);
        console.log('Cross-platform call manager initialized in hook');
      } catch (error) {
        console.error('Failed to initialize cross-platform call manager in hook:', error);
      }
    };

    initializeManager();
  }, []);

  /**
   * Handle incoming call from cross-platform notification
   * @param {Object} callData - Call information
   */
  const handleIncomingCall = useCallback((callData) => {
    console.log('Handling incoming cross-platform call:', callData);
    
    setIncomingCalls(prev => {
      const updated = new Map(prev);
      updated.set(callData.callId, callData);
      return updated;
    });

    // Set up call state listener for this call
    const stateListener = (status, data) => {
      console.log(`Call ${callData.callId} state changed to ${status}:`, data);
      
      setCallStates(prev => {
        const updated = new Map(prev);
        updated.set(callData.callId, { status, data, timestamp: Date.now() });
        return updated;
      });

      // Remove from incoming calls if accepted or declined
      if (status === 'accepted' || status === 'declined' || status === 'cancelled') {
        setIncomingCalls(prev => {
          const updated = new Map(prev);
          updated.delete(callData.callId);
          return updated;
        });
      }
    };

    crossPlatformCallManager.addCallStateListener(callData.callId, stateListener);

    // Clean up listener after call ends
    setTimeout(() => {
      crossPlatformCallManager.removeCallStateListener(callData.callId, stateListener);
    }, 300000); // 5 minutes timeout
  }, []);

  /**
   * Accept an incoming cross-platform call
   * @param {string} callId - Call identifier
   * @returns {Promise<boolean>} Success status
   */
  const acceptCall = useCallback(async (callId) => {
    try {
      console.log('Accepting cross-platform call:', callId);
      
      const callData = incomingCalls.get(callId);
      if (!callData) {
        console.error('Call data not found for call ID:', callId);
        return false;
      }

      // Handle call acceptance
      await crossPlatformCallService.handleCallAccepted(callId, callData.callerId);
      
      // Update call state
      crossPlatformCallManager.updateCallState(callId, {
        status: 'accepted',
        timestamp: Date.now()
      });

      return true;
    } catch (error) {
      console.error('Error accepting cross-platform call:', error);
      return false;
    }
  }, [incomingCalls]);

  /**
   * Reject an incoming cross-platform call
   * @param {string} callId - Call identifier
   * @returns {Promise<boolean>} Success status
   */
  const rejectCall = useCallback(async (callId) => {
    try {
      console.log('Rejecting cross-platform call:', callId);
      
      const callData = incomingCalls.get(callId);
      if (!callData) {
        console.error('Call data not found for call ID:', callId);
        return false;
      }

      // Handle call rejection
      await crossPlatformCallService.handleCallRejected(callId, callData.callerId);
      
      // Update call state
      crossPlatformCallManager.updateCallState(callId, {
        status: 'rejected',
        timestamp: Date.now()
      });

      return true;
    } catch (error) {
      console.error('Error rejecting cross-platform call:', error);
      return false;
    }
  }, [incomingCalls]);

  /**
   * Cancel an outgoing cross-platform call
   * @param {string} callId - Call identifier
   * @returns {Promise<boolean>} Success status
   */
  const cancelCall = useCallback(async (callId) => {
    try {
      console.log('Cancelling cross-platform call:', callId);
      
      // Handle call cancellation
      await crossPlatformCallService.cancelCall(callId);
      
      // Update call state
      crossPlatformCallManager.updateCallState(callId, {
        status: 'cancelled',
        timestamp: Date.now()
      });

      return true;
    } catch (error) {
      console.error('Error cancelling cross-platform call:', error);
      return false;
    }
  }, []);

  /**
   * Get call state for a specific call
   * @param {string} callId - Call identifier
   * @returns {Object|null} Call state or null if not found
   */
  const getCallState = useCallback((callId) => {
    return callStates.get(callId) || crossPlatformCallManager.getCallState(callId);
  }, [callStates]);

  /**
   * Check if there are any incoming calls
   * @returns {boolean} True if there are incoming calls
   */
  const hasIncomingCalls = useCallback(() => {
    return incomingCalls.size > 0;
  }, [incomingCalls]);

  /**
   * Get all incoming calls
   * @returns {Array} Array of incoming call data
   */
  const getIncomingCalls = useCallback(() => {
    return Array.from(incomingCalls.values());
  }, [incomingCalls]);

  /**
   * Clear all call states and incoming calls
   */
  const clearAllCalls = useCallback(() => {
    setIncomingCalls(new Map());
    setCallStates(new Map());
    crossPlatformCallManager.clearAllCallStates();
  }, []);

  // Set up global incoming call handler
  useEffect(() => {
    if (isInitialized) {
      // This would be called by the cross-platform call manager
      // when an incoming call notification is received
      window.handleIncomingCrossPlatformCall = handleIncomingCall;
      
      return () => {
        delete window.handleIncomingCrossPlatformCall;
      };
    }
  }, [isInitialized, handleIncomingCall]);

  return {
    isInitialized,
    incomingCalls: Array.from(incomingCalls.values()),
    callStates: Object.fromEntries(callStates),
    acceptCall,
    rejectCall,
    cancelCall,
    getCallState,
    hasIncomingCalls,
    getIncomingCalls,
    clearAllCalls,
    handleIncomingCall
  };
};
