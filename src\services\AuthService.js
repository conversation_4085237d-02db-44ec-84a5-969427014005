import axios from "axios";
import { firebaseService } from "./FirebaseService";

const API_URL = "http://meetagora-hfdjcgbcdrepbuc0.southeastasia-01.azurewebsites.net/";

const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

/**
 * Detect user's region/country code from browser locale information
 * @returns {string} Country code (e.g., "US", "GB", "SG")
 */
const detectUserRegion = () => {
  try {
    // Method 1: Try to get country from Intl.DateTimeFormat
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    if (timeZone) {
      // Extract country code from timezone (e.g., "America/New_York" -> "US")
      const timeZoneToCountry = {
        'America/New_York': 'US',
        'America/Los_Angeles': 'US',
        'America/Chicago': 'US',
        'America/Denver': 'US',
        'Europe/London': 'GB',
        'Europe/Paris': 'FR',
        'Europe/Berlin': 'DE',
        'Asia/Singapore': 'SG',
        'Asia/Tokyo': 'JP',
        'Asia/Shanghai': 'CN',
        'Asia/Hong_Kong': 'HK',
        'Asia/Seoul': 'KR',
        'Asia/Kolkata': 'IN',
        'Australia/Sydney': 'AU',
        'Australia/Melbourne': 'AU',
      };

      if (timeZoneToCountry[timeZone]) {
        return timeZoneToCountry[timeZone];
      }
    }

    // Method 2: Try to get country from navigator.language
    const language = navigator.language || navigator.userLanguage;
    if (language) {
      // Extract country code from language (e.g., "en-US" -> "US")
      const parts = language.split('-');
      if (parts.length > 1) {
        return parts[1].toUpperCase();
      }
    }

    // Method 3: Fallback - try to detect from timezone offset and common patterns
    const offset = new Date().getTimezoneOffset();
    if (offset === 0) return 'GB'; // GMT
    if (offset >= 300 && offset <= 360) return 'US'; // EST/CST
    if (offset >= 420 && offset <= 480) return 'US'; // MST/PST
    if (offset === -480) return 'SG'; // Singapore time
    if (offset === -540) return 'JP'; // Japan time

    // Default fallback
    return 'US';
  } catch (error) {
    console.warn('Failed to detect user region:', error);
    return 'US'; // Default fallback
  }
};

/**
 * Generate FCM token for push notifications
 * @returns {Promise<string|null>} FCM token or null if generation fails
 */
const generateFCMToken = async () => {
  try {
    console.log('Attempting to generate FCM token...');
    const token = await firebaseService.requestPermissionAndGetToken();
    if (token) {
      console.log('FCM token generated successfully');
      return token;
    } else {
      console.warn('FCM token generation returned null');
      return null;
    }
  } catch (error) {
    console.warn('FCM token generation failed:', error);
    // Don't throw error - FCM failure should not block login
    return null;
  }
};

// Auth service methods
const AuthService = {
  login: async (username, password, meetingId) => {
    // Generate FCM token before login (don't block login if it fails)
    console.log('Generating FCM token for login...');
    const fcmToken = await generateFCMToken();

    // Detect user region
    const region = detectUserRegion();
    console.log('Detected user region:', region);

    // Prepare login payload with FCM token and region
    const loginPayload = {
      username,
      password,
      meetingid: meetingId,
    };

    // Add FCM token if available
    if (fcmToken) {
      loginPayload.fcmToken = fcmToken;
      console.log('Including FCM token in login request');
    } else {
      console.warn('No FCM token available - proceeding with login without FCM');
    }

    // Add region information
    loginPayload.region = region;

    console.log('Login payload prepared:', {
      ...loginPayload,
      fcmToken: fcmToken ? `${fcmToken.substring(0, 20)}...` : 'none'
    });

    const response = await api.post("/auth/login", loginPayload);

    if (response.data.tokens) {
      // Store initial tokens
      let tokens = response.data.tokens;

      localStorage.setItem("userData", JSON.stringify(response.data.user));

      if (response.data.event) {
        localStorage.setItem("eventData", JSON.stringify(response.data.event));
      }

      if (response.data.eventSchedule) {
        localStorage.setItem("eventSchedule", JSON.stringify(response.data.eventSchedule));
      }

      if (response.data.sponsors) {
        localStorage.setItem("sponsors", JSON.stringify(response.data.sponsors));
      }

      if (response.data.rooms) {
        localStorage.setItem("rooms", JSON.stringify(response.data.rooms));
      }

      if (response.data.attendees) {
        localStorage.setItem("attendees", JSON.stringify(response.data.attendees));
      }

      // Skip RTC token fetching during login to optimize performance
      // RTC tokens will be fetched only when actually making calls
      console.log("Skipping RTC token fetch during login - will fetch when needed for calls");

      // Store the complete tokens object (including RTC token)
      localStorage.setItem("tokens", JSON.stringify(tokens));

      // Update response data to include the RTC token
      response.data.tokens = tokens;
    }

    return response.data;
  },

  fetchRTCToken: async (callerId, channelName = null) => {
    try {
      // Prepare request body with proper channel name
      const requestBody = {
        userId: callerId,
        channelName: channelName || '', // Use provided channel name or empty string for generic token
      };

      console.log("Fetching RTC token with params:", requestBody);

      const response = await api.post("/calls/calling", requestBody);

      if (response.data?.token) {
        // Only update localStorage if this is a generic token request (no specific channel)
        // Channel-specific tokens should be returned directly without caching
        if (!channelName) {
          // Get current tokens from localStorage
          const tokensStr = localStorage.getItem("tokens");
          const tokens = tokensStr ? JSON.parse(tokensStr) : {};

          // Update with new RTC token and store token creation time
          tokens.rtcToken = response.data.token;
          tokens.rtcTokenCreatedAt = Date.now(); // Store actual creation time

          // Save updated tokens back to localStorage
          localStorage.setItem("tokens", JSON.stringify(tokens));

          console.log("Generic RTC token updated successfully in localStorage");
        } else {
          console.log(`Channel-specific RTC token fetched for channel: ${channelName}`);
        }

        return response.data.token;
      } else {
        console.error("Invalid token response:", response.data);
        throw new Error("Invalid token response from server");
      }
    } catch (error) {
      console.error("Failed to fetch RTC token:", error);
      throw error;
    }
  },

  refreshToken: async (agoraId) => {
    try {
      const response = await api.post("/auth/refresh-token", {
        agoraid: agoraId,
      });

      const newTokens = response.data.tokens;

      if (newTokens) {
        // Also refresh RTC token
        try {
          const rtcToken = await AuthService.fetchRTCToken(agoraId);
          newTokens.rtcToken = rtcToken;
          newTokens.rtcTokenCreatedAt = Date.now();
        } catch (rtcError) {
          console.error("Failed to refresh RTC token:", rtcError);
          // Continue with other tokens even if RTC token refresh fails

          // Try to preserve existing RTC token if available
          const existingTokens = JSON.parse(localStorage.getItem("tokens") || "{}");
          if (existingTokens.rtcToken) {
            newTokens.rtcToken = existingTokens.rtcToken;
            newTokens.rtcTokenCreatedAt = existingTokens.rtcTokenCreatedAt;
            console.log("Preserved existing RTC token during refresh");
          }
        }

        localStorage.setItem("tokens", JSON.stringify(newTokens));
      }

      return newTokens;
    } catch (error) {
      console.error("🔁 Failed to refresh token:", error);
      throw error;
    }
  },

  logout: async () => {
    try {
      const tokens = JSON.parse(localStorage.getItem("tokens"));

      if (tokens?.refreshToken) {
        await api.post("/auth/logout", {
          refreshToken: tokens.refreshToken
        });
      }
    } catch (error) {
      console.error("Logout server error:", error);
    } finally {
      localStorage.removeItem("tokens");
      localStorage.removeItem("userData");
      localStorage.removeItem("eventData");
      localStorage.removeItem("eventSchedule");
      localStorage.removeItem("sponsors");
      localStorage.removeItem("rooms");
      localStorage.removeItem("attendees");
    }
  }
};

// Get RTM token
const getRtmToken = () => {
  const rtmTokenData = localStorage.getItem("tokens");
  if (rtmTokenData) {
    const tokens = JSON.parse(rtmTokenData);
    return tokens.rtmToken;
  }
  return null;
};

// Get RTC token
const getRtcToken = () => {
  const tokenData = localStorage.getItem("tokens");
  if (tokenData) {
    const tokens = JSON.parse(tokenData);
    return tokens.rtcToken;
  }
  return null;
};

// Check if a token is valid (not expired)
// Agora tokens have a format that includes expiration info
const isTokenValid = (token, createdAt = null) => {
  // First check if token exists
  if (!token) return false;

  // If we have a creation timestamp, check if token is about to expire
  // For RTC tokens, we'll consider them invalid after 50 minutes to allow time for refresh
  // For other tokens, we'll use 23 hours as they typically last 24 hours
  if (createdAt) {
    const tokenAge = Date.now() - createdAt;

    // For RTC tokens (shorter lifespan), use 50 minutes
    // For other tokens, use 23 hours
    const maxTokenAge = 50 * 60 * 1000; // 50 minutes in milliseconds for RTC tokens

    if (tokenAge > maxTokenAge) {
      console.warn(`Token is approaching expiration (created over ${maxTokenAge / (60 * 1000)} minutes ago)`);
      return false;
    }
  }

  return true;
};

// Check if RTC token is valid
const isRTCTokenValid = () => {
  const tokenData = localStorage.getItem("tokens");
  if (tokenData) {
    const tokens = JSON.parse(tokenData);
    return isTokenValid(tokens.rtcToken, tokens.rtcTokenCreatedAt);
  }
  return false;
};

// Check if RTM token is valid
const isRTMTokenValid = () => {
  const token = getRtmToken();
  return isTokenValid(token);
};

// Check if tokens are valid before making calls and fetch channel-specific RTC token
const checkTokensBeforeCall = async (agoraId, channelName = null) => {
  try {
    const tokenData = localStorage.getItem("tokens");
    if (!tokenData) {
      throw new Error("No tokens found. Please log in again.");
    }

    // Check RTM token validity first (needed for signaling)
    if (!isRTMTokenValid()) {
      console.log("RTM token invalid or expired, refreshing all tokens...");
      try {
        await AuthService.refreshToken(agoraId);
      } catch (error) {
        console.error("Failed to refresh RTM token:", error);
        throw new Error("Failed to refresh RTM token before call");
      }
    }

    // For calls, always fetch a fresh channel-specific RTC token
    if (channelName) {
      console.log(`Fetching channel-specific RTC token for channel: ${channelName}`);
      try {
        const channelToken = await AuthService.fetchRTCToken(agoraId, channelName);
        return { rtcToken: channelToken, isChannelSpecific: true };
      } catch (error) {
        console.error("Failed to fetch channel-specific RTC token:", error);
        throw new Error("Failed to fetch channel-specific RTC token for call");
      }
    } else {
      // Fallback: check generic RTC token validity
      if (!isRTCTokenValid()) {
        console.log("Generic RTC token invalid or expired, refreshing...");
        try {
          await AuthService.fetchRTCToken(agoraId);
        } catch (error) {
          console.error("Failed to refresh generic RTC token:", error);
          throw new Error("Failed to refresh RTC token before call");
        }
      }
      return { rtcToken: getRtcToken(), isChannelSpecific: false };
    }
  } catch (error) {
    console.error("Token validation failed before call:", error);
    throw error;
  }
};

export {
  api,
  AuthService,
  getRtmToken,
  getRtcToken,
  isRTCTokenValid,
  isRTMTokenValid,
  checkTokensBeforeCall
};