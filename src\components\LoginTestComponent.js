import React, { useState } from 'react';
import { AuthService } from '../services/AuthService';
import { firebaseService } from '../services/FirebaseService';

/**
 * Test component for login functionality with FCM token generation
 * This component can be used to test the enhanced login flow
 */
const LoginTestComponent = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [fcmToken, setFcmToken] = useState(null);

  const addTestResult = (test, result, details = '') => {
    setTestResults(prev => [...prev, {
      test,
      result,
      details,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testFCMTokenGeneration = async () => {
    try {
      addTestResult('FCM Token Generation', 'RUNNING', 'Requesting FCM token...');
      
      const token = await firebaseService.requestPermissionAndGetToken();
      
      if (token) {
        setFcmToken(token);
        addTestResult(
          'FCM Token Generation',
          'PASS',
          `Token generated: ${token.substring(0, 30)}...`
        );
      } else {
        addTestResult(
          'FCM Token Generation',
          'FAIL',
          'No token returned (check Firebase config and permissions)'
        );
      }
    } catch (error) {
      addTestResult('FCM Token Generation', 'ERROR', error.message);
    }
  };

  const testRegionDetection = () => {
    try {
      // Test the region detection logic (copied from AuthService)
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const language = navigator.language || navigator.userLanguage;
      const offset = new Date().getTimezoneOffset();

      addTestResult(
        'Region Detection',
        'PASS',
        `TimeZone: ${timeZone}, Language: ${language}, Offset: ${offset}`
      );
    } catch (error) {
      addTestResult('Region Detection', 'ERROR', error.message);
    }
  };

  const testLoginPayloadStructure = async () => {
    try {
      addTestResult('Login Payload Test', 'RUNNING', 'Testing login payload structure...');

      // Mock the login payload creation logic
      const mockUsername = 'testuser';
      const mockPassword = 'testpass';
      const mockMeetingId = 'test123';
      
      const loginPayload = {
        username: mockUsername,
        password: mockPassword,
        meetingid: mockMeetingId,
      };

      // Add FCM token if available
      if (fcmToken) {
        loginPayload.fcmToken = fcmToken;
      }

      // Add region
      const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      let region = 'US'; // default
      if (timeZone) {
        const timeZoneToCountry = {
          'America/New_York': 'US',
          'Europe/London': 'GB',
          'Asia/Singapore': 'SG',
          'Asia/Tokyo': 'JP',
        };
        region = timeZoneToCountry[timeZone] || 'US';
      }
      loginPayload.region = region;

      addTestResult(
        'Login Payload Test',
        'PASS',
        `Payload structure correct. Region: ${region}, FCM: ${fcmToken ? 'included' : 'not available'}`
      );

      console.log('Test login payload:', {
        ...loginPayload,
        fcmToken: fcmToken ? `${fcmToken.substring(0, 20)}...` : 'none'
      });

    } catch (error) {
      addTestResult('Login Payload Test', 'ERROR', error.message);
    }
  };

  const runAllTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    await testFCMTokenGeneration();
    testRegionDetection();
    await testLoginPayloadStructure();

    setIsRunningTests(false);
  };

  const clearResults = () => {
    setTestResults([]);
    setFcmToken(null);
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px', borderRadius: '8px' }}>
      <h3>Enhanced Login Test Console</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <h4>Current Status</h4>
        <p>FCM Token: {fcmToken ? '✅ Generated' : '❌ Not available'}</p>
        <p>Firebase Config: {firebaseService.isConfigured ? '✅ Configured' : '❌ Not configured'}</p>
        <p>Browser Language: {navigator.language}</p>
        <p>Timezone: {Intl.DateTimeFormat().resolvedOptions().timeZone}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h4>Test Actions</h4>
        <button 
          onClick={runAllTests} 
          disabled={isRunningTests}
          style={{ margin: '5px', padding: '8px 16px', backgroundColor: '#007bff', color: 'white', border: 'none' }}
        >
          {isRunningTests ? 'Running Tests...' : 'Run All Tests'}
        </button>
        
        <button 
          onClick={testFCMTokenGeneration}
          style={{ margin: '5px', padding: '8px 16px' }}
        >
          Test FCM Token Only
        </button>
        
        <button 
          onClick={testRegionDetection}
          style={{ margin: '5px', padding: '8px 16px' }}
        >
          Test Region Detection
        </button>
        
        <button 
          onClick={clearResults}
          style={{ margin: '5px', padding: '8px 16px' }}
        >
          Clear Results
        </button>
      </div>

      {fcmToken && (
        <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#d4edda', border: '1px solid #c3e6cb' }}>
          <h4>FCM Token Generated</h4>
          <p style={{ wordBreak: 'break-all', fontSize: '0.8em' }}>
            <strong>Token:</strong> {fcmToken}
          </p>
        </div>
      )}

      <div>
        <h4>Test Results</h4>
        <div style={{ maxHeight: '300px', overflowY: 'auto', border: '1px solid #ddd', padding: '10px' }}>
          {testResults.length === 0 ? (
            <p>No test results yet. Run some tests to see results here.</p>
          ) : (
            testResults.map((result, index) => (
              <div 
                key={index} 
                style={{ 
                  margin: '5px 0', 
                  padding: '8px', 
                  backgroundColor: result.result === 'PASS' ? '#d4edda' : 
                                   result.result === 'FAIL' ? '#f8d7da' : 
                                   result.result === 'ERROR' ? '#f5c6cb' : '#fff3cd',
                  border: '1px solid #ccc',
                  borderRadius: '4px'
                }}
              >
                <strong>[{result.timestamp}] {result.test}:</strong> 
                <span style={{ 
                  color: result.result === 'PASS' ? '#155724' : 
                         result.result === 'FAIL' ? '#721c24' : 
                         result.result === 'ERROR' ? '#721c24' : '#856404',
                  fontWeight: 'bold',
                  marginLeft: '10px'
                }}>
                  {result.result}
                </span>
                {result.details && <div style={{ marginTop: '5px', fontSize: '0.9em' }}>{result.details}</div>}
              </div>
            ))
          )}
        </div>
      </div>

      <div style={{ marginTop: '20px', padding: '10px', backgroundColor: '#f8f9fa', border: '1px solid #dee2e6' }}>
        <h4>Usage Instructions</h4>
        <ol>
          <li>Click "Run All Tests" to test FCM token generation and region detection</li>
          <li>Check browser console for detailed logs</li>
          <li>Ensure notification permissions are granted when prompted</li>
          <li>Verify that Firebase configuration is correct</li>
          <li>The enhanced login will now include FCM token and region in the API request</li>
        </ol>
      </div>
    </div>
  );
};

export default LoginTestComponent;
