import React, { useState, useEffect } from 'react';
import { useCrossPlatformCall } from '../hooks/useCrossPlatformCall';
import { crossPlatformCallService } from '../services/CrossPlatformCallService';
import { firebaseService } from '../services/FirebaseService';

/**
 * Test component for cross-platform calling functionality
 * This component can be used to test and debug cross-platform calls
 */
const CrossPlatformCallTest = () => {
  const [testResults, setTestResults] = useState([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [fcmToken, setFcmToken] = useState(null);
  const [testCallId, setTestCallId] = useState(null);

  const {
    isInitialized,
    incomingCalls,
    acceptCall,
    rejectCall,
    hasIncomingCalls
  } = useCrossPlatformCall();

  useEffect(() => {
    // Get FCM token for testing
    const getFcmToken = async () => {
      const token = await firebaseService.requestPermissionAndGetToken();
      setFcmToken(token);
    };

    if (isInitialized) {
      getFcmToken();
    }
  }, [isInitialized]);

  const addTestResult = (test, result, details = '') => {
    setTestResults(prev => [...prev, {
      test,
      result,
      details,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const runBasicTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    try {
      // Test 1: Check if cross-platform manager is initialized
      addTestResult(
        'Cross-platform manager initialization',
        isInitialized ? 'PASS' : 'FAIL',
        isInitialized ? 'Manager initialized successfully' : 'Manager not initialized'
      );

      // Test 2: Check FCM token availability
      addTestResult(
        'FCM token generation',
        fcmToken ? 'PASS' : 'FAIL',
        fcmToken ? `Token: ${fcmToken.substring(0, 20)}...` : 'No FCM token available'
      );

      // Test 3: Test Firebase service configuration
      const isFirebaseConfigured = firebaseService.isConfigured;
      addTestResult(
        'Firebase configuration',
        isFirebaseConfigured ? 'PASS' : 'FAIL',
        isFirebaseConfigured ? 'Firebase properly configured' : 'Firebase configuration missing'
      );

      // Test 4: Test cross-platform call service
      const pendingCalls = crossPlatformCallService.getPendingCalls();
      addTestResult(
        'Cross-platform call service',
        'PASS',
        `Service available, ${pendingCalls.length} pending calls`
      );

    } catch (error) {
      addTestResult('Basic tests', 'ERROR', error.message);
    }

    setIsRunningTests(false);
  };

  const testCallInitiation = async () => {
    try {
      // Mock call data for testing
      const mockCallData = {
        callerId: 'test_caller_123',
        calleeId: 'test_callee_456',
        eventCode: 'TEST_EVENT',
        channelName: 'test_channel_' + Date.now(),
        isVideoCall: false,
        callerName: 'Test Caller'
      };

      addTestResult('Call initiation test', 'RUNNING', 'Initiating test call...');

      const result = await crossPlatformCallService.initiateCall(mockCallData);
      
      if (result.success) {
        setTestCallId(result.callId);
        addTestResult(
          'Call initiation test',
          'PASS',
          `Call initiated successfully. Call ID: ${result.callId}`
        );
      } else {
        addTestResult('Call initiation test', 'FAIL', 'Call initiation failed');
      }

    } catch (error) {
      addTestResult('Call initiation test', 'ERROR', error.message);
    }
  };

  const testCallCancellation = async () => {
    if (!testCallId) {
      addTestResult('Call cancellation test', 'SKIP', 'No active test call to cancel');
      return;
    }

    try {
      await crossPlatformCallService.cancelCall(testCallId);
      addTestResult(
        'Call cancellation test',
        'PASS',
        `Call ${testCallId} cancelled successfully`
      );
      setTestCallId(null);
    } catch (error) {
      addTestResult('Call cancellation test', 'ERROR', error.message);
    }
  };

  const simulateIncomingCall = () => {
    const mockIncomingCall = {
      callId: 'mock_incoming_' + Date.now(),
      callerId: 'mock_caller_789',
      callerName: 'Mock Caller',
      channelName: 'mock_channel_' + Date.now(),
      isVideoCall: false,
      timestamp: Date.now()
    };

    // Simulate incoming call notification
    if (window.handleIncomingCrossPlatformCall) {
      window.handleIncomingCrossPlatformCall(mockIncomingCall);
      addTestResult(
        'Simulate incoming call',
        'PASS',
        `Simulated incoming call from ${mockIncomingCall.callerName}`
      );
    } else {
      addTestResult(
        'Simulate incoming call',
        'FAIL',
        'Incoming call handler not available'
      );
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px', borderRadius: '8px' }}>
      <h3>Cross-Platform Calling Test Console</h3>
      
      <div style={{ marginBottom: '20px' }}>
        <h4>Status</h4>
        <p>Manager Initialized: {isInitialized ? '✅' : '❌'}</p>
        <p>FCM Token: {fcmToken ? '✅' : '❌'}</p>
        <p>Incoming Calls: {hasIncomingCalls() ? `${incomingCalls.length} calls` : 'None'}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h4>Test Actions</h4>
        <button 
          onClick={runBasicTests} 
          disabled={isRunningTests}
          style={{ margin: '5px', padding: '8px 16px' }}
        >
          {isRunningTests ? 'Running...' : 'Run Basic Tests'}
        </button>
        
        <button 
          onClick={testCallInitiation}
          style={{ margin: '5px', padding: '8px 16px' }}
        >
          Test Call Initiation
        </button>
        
        <button 
          onClick={testCallCancellation}
          disabled={!testCallId}
          style={{ margin: '5px', padding: '8px 16px' }}
        >
          Test Call Cancellation
        </button>
        
        <button 
          onClick={simulateIncomingCall}
          style={{ margin: '5px', padding: '8px 16px' }}
        >
          Simulate Incoming Call
        </button>
        
        <button 
          onClick={clearResults}
          style={{ margin: '5px', padding: '8px 16px' }}
        >
          Clear Results
        </button>
      </div>

      {incomingCalls.length > 0 && (
        <div style={{ marginBottom: '20px', padding: '10px', backgroundColor: '#f0f8ff', border: '1px solid #0066cc' }}>
          <h4>Incoming Calls</h4>
          {incomingCalls.map(call => (
            <div key={call.callId} style={{ margin: '10px 0', padding: '10px', backgroundColor: 'white', border: '1px solid #ccc' }}>
              <p><strong>From:</strong> {call.callerName} ({call.callerId})</p>
              <p><strong>Type:</strong> {call.isVideoCall ? 'Video' : 'Voice'} Call</p>
              <p><strong>Call ID:</strong> {call.callId}</p>
              <button 
                onClick={() => acceptCall(call.callId)}
                style={{ margin: '5px', padding: '5px 10px', backgroundColor: '#28a745', color: 'white', border: 'none' }}
              >
                Accept
              </button>
              <button 
                onClick={() => rejectCall(call.callId)}
                style={{ margin: '5px', padding: '5px 10px', backgroundColor: '#dc3545', color: 'white', border: 'none' }}
              >
                Reject
              </button>
            </div>
          ))}
        </div>
      )}

      <div>
        <h4>Test Results</h4>
        <div style={{ maxHeight: '300px', overflowY: 'auto', border: '1px solid #ddd', padding: '10px' }}>
          {testResults.length === 0 ? (
            <p>No test results yet. Run some tests to see results here.</p>
          ) : (
            testResults.map((result, index) => (
              <div 
                key={index} 
                style={{ 
                  margin: '5px 0', 
                  padding: '8px', 
                  backgroundColor: result.result === 'PASS' ? '#d4edda' : 
                                   result.result === 'FAIL' ? '#f8d7da' : 
                                   result.result === 'ERROR' ? '#f5c6cb' : '#fff3cd',
                  border: '1px solid #ccc',
                  borderRadius: '4px'
                }}
              >
                <strong>[{result.timestamp}] {result.test}:</strong> 
                <span style={{ 
                  color: result.result === 'PASS' ? '#155724' : 
                         result.result === 'FAIL' ? '#721c24' : 
                         result.result === 'ERROR' ? '#721c24' : '#856404',
                  fontWeight: 'bold',
                  marginLeft: '10px'
                }}>
                  {result.result}
                </span>
                {result.details && <div style={{ marginTop: '5px', fontSize: '0.9em' }}>{result.details}</div>}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default CrossPlatformCallTest;
