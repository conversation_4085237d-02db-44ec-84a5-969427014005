import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, onMessage } from 'firebase/messaging';
import { firebaseConfig, vapidKey, validateFirebaseConfig } from '../config/firebase-config';

// Validate Firebase configuration
const isConfigValid = validateFirebaseConfig();

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging and get a reference to the service
const messaging = isConfigValid ? getMessaging(app) : null;

/**
 * Firebase Cloud Messaging Service for cross-platform push notifications
 * This service handles FCM token management and push notification sending
 */
export class FirebaseService {
  constructor() {
    this.messaging = messaging;
    this.fcmToken = null;
    this.isConfigured = isConfigValid;
  }

  /**
   * Request permission for notifications and get FCM token
   * @returns {Promise<string|null>} FCM token or null if permission denied
   */
  async requestPermissionAndGetToken() {
    try {
      if (!this.isConfigured || !this.messaging) {
        console.warn('Firebase not properly configured. Cannot request FCM token.');
        return null;
      }

      // Request permission for notifications
      const permission = await Notification.requestPermission();

      if (permission === 'granted') {
        console.log('Notification permission granted.');

        // Get FCM token
        const token = await getToken(this.messaging, {
          vapidKey: vapidKey
        });
        
        if (token) {
          console.log('FCM Token:', token);
          this.fcmToken = token;
          
          // Store token in localStorage for persistence
          localStorage.setItem('fcmToken', token);
          
          return token;
        } else {
          console.log('No registration token available.');
          return null;
        }
      } else {
        console.log('Unable to get permission to notify.');
        return null;
      }
    } catch (error) {
      console.error('An error occurred while retrieving token:', error);
      return null;
    }
  }

  /**
   * Get stored FCM token
   * @returns {string|null} Stored FCM token
   */
  getStoredToken() {
    if (this.fcmToken) {
      return this.fcmToken;
    }
    
    const storedToken = localStorage.getItem('fcmToken');
    if (storedToken) {
      this.fcmToken = storedToken;
      return storedToken;
    }
    
    return null;
  }

  /**
   * Set up foreground message listener
   * @param {Function} callback - Callback function to handle incoming messages
   */
  setupForegroundMessageListener(callback) {
    onMessage(this.messaging, (payload) => {
      console.log('Message received in foreground:', payload);
      
      // Handle the message based on type
      if (payload.data && payload.data.type === 'call_request') {
        // This is a call notification
        const callData = {
          callId: payload.data.callId,
          callerId: payload.data.callerId,
          callerName: payload.data.callerName,
          channelName: payload.data.channelName,
          isVideoCall: payload.data.isVideoCall === 'true',
          timestamp: parseInt(payload.data.timestamp)
        };
        
        callback(callData);
      }
    });
  }

  /**
   * Send FCM notification to a specific device
   * This would typically be called from the backend, but included here for reference
   * @param {string} targetToken - FCM token of the target device
   * @param {Object} callData - Call information
   * @returns {Promise<boolean>} Success status
   */
  async sendCallNotification(targetToken, callData) {
    try {
      // This should be implemented on the backend server
      // The web client cannot directly send FCM messages due to security restrictions
      // This is just a placeholder to show the expected structure
      
      const notificationPayload = {
        to: targetToken,
        data: {
          type: 'call_request',
          callId: callData.callId,
          callerId: callData.callerId,
          callerName: callData.callerName,
          channelName: callData.channelName,
          isVideoCall: callData.isVideoCall.toString(),
          timestamp: callData.timestamp.toString()
        },
        notification: {
          title: `Incoming ${callData.isVideoCall ? 'Video' : 'Voice'} Call`,
          body: `${callData.callerName} is calling you`,
          icon: '/logo192.png',
          click_action: 'FLUTTER_NOTIFICATION_CLICK'
        }
      };

      console.log('FCM notification payload:', notificationPayload);
      
      // In a real implementation, this would be sent to your backend server
      // which would then use the Firebase Admin SDK to send the notification
      return true;
    } catch (error) {
      console.error('Error sending FCM notification:', error);
      return false;
    }
  }

  /**
   * Clear stored FCM token
   */
  clearToken() {
    this.fcmToken = null;
    localStorage.removeItem('fcmToken');
  }
}

// Export singleton instance
export const firebaseService = new FirebaseService();
